Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    gd32f4xx_it.o(i.SDIO_IRQHandler) refers to sdio_sdcard.o(i.sd_interrupts_process) for sd_interrupts_process
    gd32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.delay_decrement) for delay_decrement
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_transfer_number_get) for dma_transfer_number_get
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to rt_memclr.o(.text) for __aeabi_memclr
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_transfer_number_config) for dma_transfer_number_config
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to mcu_cmic_gd32f470vet6.o(.bss) for rxbuffer
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to usart_app.o(.bss) for uart_dma_buffer
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to usart_app.o(.data) for rx_flag
    main.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    main.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    main.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    main.o(i.fputc) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    main.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    main.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    main.o(i.main) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    main.o(i.main) refers to systick.o(i.systick_config) for systick_config
    main.o(i.main) refers to perf_counter.o(i.init_cycle_counter) for init_cycle_counter
    main.o(i.main) refers to perf_counter.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to system_gd32f4xx.o(i.gd32f4xx_firmware_version_get) for gd32f4xx_firmware_version_get
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_led_init) for bsp_led_init
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_btn_init) for bsp_btn_init
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) for bsp_oled_init
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) for bsp_gd25qxx_init
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) for bsp_usart_init
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) for bsp_adc_init
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) for bsp_dac_init
    main.o(i.main) refers to usart_app.o(i.my_printf) for my_printf
    main.o(i.main) refers to gd25qxx.o(i.spi_flash_read_id) for spi_flash_read_id
    main.o(i.main) refers to sd_app.o(i.sd_fatfs_init) for sd_fatfs_init
    main.o(i.main) refers to btn_app.o(i.app_btn_init) for app_btn_init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to rtc_app.o(i.rtc_app_init) for rtc_app_init
    main.o(i.main) refers to sampling_app.o(i.sampling_app_init) for sampling_app_init
    main.o(i.main) refers to config_app.o(i.config_app_init) for config_app_init
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    systick.o(i.delay_1ms) refers to systick.o(.data) for delay
    systick.o(i.delay_decrement) refers to systick.o(.data) for delay
    systick.o(i.systick_config) refers to systick.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    systick.o(i.systick_config) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    mcu_cmic_gd32f470vet6.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_dma.o(i.dma_circulation_enable) for dma_circulation_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_sync_mode_config) for adc_sync_mode_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_special_function_config) for adc_special_function_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_channel_length_config) for adc_channel_length_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_routine_channel_config) for adc_routine_channel_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_external_trigger_source_config) for adc_external_trigger_source_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_dma_request_after_last_enable) for adc_dma_request_after_last_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_dma_mode_enable) for adc_dma_mode_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to systick.o(i.delay_1ms) for delay_1ms
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_software_trigger_enable) for adc_software_trigger_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to mcu_cmic_gd32f470vet6.o(.data) for adc_value
    mcu_cmic_gd32f470vet6.o(i.bsp_btn_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_btn_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_btn_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dac.o(i.dac_deinit) for dac_deinit
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dac.o(i.dac_trigger_source_config) for dac_trigger_source_config
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dac.o(i.dac_trigger_enable) for dac_trigger_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dac.o(i.dac_wave_mode_config) for dac_wave_mode_config
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dac.o(i.dac_enable) for dac_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dac.o(i.dac_dma_enable) for dac_dma_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to mcu_cmic_gd32f470vet6.o(i.timer5_config) for timer5_config
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to mcu_cmic_gd32f470vet6.o(.data) for convertarr
    mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) refers to gd25qxx.o(i.spi_flash_init) for spi_flash_init
    mcu_cmic_gd32f470vet6.o(i.bsp_led_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_led_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_led_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    mcu_cmic_gd32f470vet6.o(i.bsp_led_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_i2c.o(i.i2c_clock_config) for i2c_clock_config
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_i2c.o(i.i2c_mode_addr_config) for i2c_mode_addr_config
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_i2c.o(i.i2c_enable) for i2c_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_i2c.o(i.i2c_ack_config) for i2c_ack_config
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_dma.o(i.dma_single_data_para_struct_init) for dma_single_data_para_struct_init
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_dma.o(i.dma_circulation_disable) for dma_circulation_disable
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to mcu_cmic_gd32f470vet6.o(.data) for oled_data_buf
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_dma.o(i.dma_circulation_disable) for dma_circulation_disable
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_usart.o(i.usart_dma_receive_config) for usart_dma_receive_config
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to mcu_cmic_gd32f470vet6.o(.bss) for rxbuffer
    mcu_cmic_gd32f470vet6.o(i.timer5_config) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.timer5_config) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    mcu_cmic_gd32f470vet6.o(i.timer5_config) refers to gd32f4xx_timer.o(i.timer_struct_para_init) for timer_struct_para_init
    mcu_cmic_gd32f470vet6.o(i.timer5_config) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    mcu_cmic_gd32f470vet6.o(i.timer5_config) refers to gd32f4xx_timer.o(i.timer_master_output_trigger_source_select) for timer_master_output_trigger_source_select
    mcu_cmic_gd32f470vet6.o(i.timer5_config) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    mcu_cmic_gd32f470vet6.o(.bss) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_buffer_read) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_buffer_read) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_buffer_read) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_buffer_read) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_buffer_write) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_buffer_write) refers to gd25qxx.o(i.spi_flash_page_write) for spi_flash_page_write
    gd25qxx.o(i.spi_flash_bulk_erase) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    gd25qxx.o(i.spi_flash_page_write) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_page_write) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_page_write) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_read_id) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_read_id) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_sector_erase) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_send_byte_dma) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to mcu_cmic_gd32f470vet6.o(.bss) for spi1_send_array
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to mcu_cmic_gd32f470vet6.o(.bss) for spi1_send_array
    gd25qxx.o(i.spi_flash_start_read_sequence) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_start_read_sequence) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_start_read_sequence) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to mcu_cmic_gd32f470vet6.o(.bss) for spi1_send_array
    gd25qxx.o(i.spi_flash_wait_for_dma_end) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_wait_for_dma_end) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_wait_for_dma_end) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_write_enable) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_write_enable) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.test_spi_flash) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.test_spi_flash) refers to usart_app.o(i.my_printf) for my_printf
    gd25qxx.o(i.test_spi_flash) refers to gd25qxx.o(i.spi_flash_init) for spi_flash_init
    gd25qxx.o(i.test_spi_flash) refers to gd25qxx.o(i.spi_flash_read_id) for spi_flash_read_id
    gd25qxx.o(i.test_spi_flash) refers to gd25qxx.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    gd25qxx.o(i.test_spi_flash) refers to gd25qxx.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    gd25qxx.o(i.test_spi_flash) refers to strlen.o(.text) for strlen
    gd25qxx.o(i.test_spi_flash) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gd25qxx.o(i.test_spi_flash) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    gd25qxx.o(i.test_spi_flash) refers to gd25qxx.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    gd25qxx.o(i.test_spi_flash) refers to aeabi_memset.o(.text) for __aeabi_memset
    gd25qxx.o(i.test_spi_flash) refers to memcmp.o(.text) for memcmp
    lfs.o(i.lfs_alignup) refers to lfs.o(i.lfs_aligndown) for lfs_aligndown
    lfs.o(i.lfs_alloc) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_alloc) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_alloc) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    lfs.o(i.lfs_alloc) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_alloc) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_alloc) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_alloc) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_alloc) refers to lfs.o(i.lfs_fs_traverse) for lfs_fs_traverse
    lfs.o(i.lfs_alloc) refers to lfs.o(i.lfs_alloc_lookahead) for lfs_alloc_lookahead
    lfs.o(i.lfs_bd_cmp) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_bd_erase) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_bd_flush) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_bd_flush) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_bd_flush) refers to lfs.o(i.lfs_cache_drop) for lfs_cache_drop
    lfs.o(i.lfs_bd_flush) refers to lfs.o(i.lfs_bd_cmp) for lfs_bd_cmp
    lfs.o(i.lfs_bd_flush) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_bd_prog) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_bd_prog) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_bd_prog) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    lfs.o(i.lfs_bd_prog) refers to lfs.o(i.lfs_max) for lfs_max
    lfs.o(i.lfs_bd_prog) refers to lfs.o(i.lfs_bd_flush) for lfs_bd_flush
    lfs.o(i.lfs_bd_prog) refers to lfs.o(i.lfs_aligndown) for lfs_aligndown
    lfs.o(i.lfs_bd_read) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_bd_read) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_bd_read) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    lfs.o(i.lfs_bd_read) refers to lfs.o(i.lfs_aligndown) for lfs_aligndown
    lfs.o(i.lfs_bd_read) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_bd_sync) refers to lfs.o(i.lfs_cache_drop) for lfs_cache_drop
    lfs.o(i.lfs_bd_sync) refers to lfs.o(i.lfs_bd_flush) for lfs_bd_flush
    lfs.o(i.lfs_bd_sync) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_cache_zero) refers to aeabi_memset.o(.text) for __aeabi_memset
    lfs.o(i.lfs_commitattr) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_commitattr) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_commitattr) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_commitattr) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_ctz) refers to lfs.o(i.lfs_npw2) for lfs_npw2
    lfs.o(i.lfs_ctz_extend) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_ctz_extend) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_ctz_extend) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_ctz_extend) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_ctz_extend) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_alloc) for lfs_alloc
    lfs.o(i.lfs_ctz_extend) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_bd_erase) for lfs_bd_erase
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_ctz_index) for lfs_ctz_index
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_ctz) for lfs_ctz
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_ctz_extend) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_cache_drop) for lfs_cache_drop
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_ctz_index) for lfs_ctz_index
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_ctz) for lfs_ctz
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_npw2) for lfs_npw2
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_ctz_find) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_ctz_fromle32) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_ctz_index) refers to lfs.o(i.lfs_popc) for lfs_popc
    lfs.o(i.lfs_ctz_tole32) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_ctz_traverse) refers to lfs.o(i.lfs_ctz_index) for lfs_ctz_index
    lfs.o(i.lfs_ctz_traverse) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_ctz_traverse) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_deinit) refers to lfs.o(i.lfs_free) for lfs_free
    lfs.o(i.lfs_dir_alloc) refers to lfs.o(i.lfs_alloc) for lfs_alloc
    lfs.o(i.lfs_dir_alloc) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_alloc) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_file_outline) for lfs_file_outline
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_dir_commit) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_tag_type1) for lfs_tag_type1
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_tag_chunk) for lfs_tag_chunk
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_xormove) for lfs_gstate_xormove
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_tag_isvalid) for lfs_tag_isvalid
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_fs_pred) for lfs_fs_pred
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_drop) for lfs_dir_drop
    lfs.o(i.lfs_dir_commit) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_traverse) for lfs_dir_traverse
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_iszero) for lfs_gstate_iszero
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_getgstate) for lfs_dir_getgstate
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_tole32) for lfs_gstate_tole32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_commitattr) for lfs_dir_commitattr
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_fromle32) for lfs_gstate_fromle32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_commitcrc) for lfs_dir_commitcrc
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_cache_drop) for lfs_cache_drop
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_compact) for lfs_dir_compact
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_commit) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_commit_commit) for lfs_dir_commit_commit
    lfs.o(i.lfs_dir_commit_commit) refers to lfs.o(i.lfs_dir_commitattr) for lfs_dir_commitattr
    lfs.o(i.lfs_dir_commit_size) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_commitattr) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_commitattr) refers to lfs.o(i.lfs_tobe32) for lfs_tobe32
    lfs.o(i.lfs_dir_commitattr) refers to lfs.o(i.lfs_dir_commitprog) for lfs_dir_commitprog
    lfs.o(i.lfs_dir_commitattr) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_tobe32) for lfs_tobe32
    lfs.o(i.lfs_dir_commitcrc) refers to lfs_util.o(i.lfs_crc) for lfs_crc
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_bd_sync) for lfs_bd_sync
    lfs.o(i.lfs_dir_commitprog) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_dir_commitprog) refers to lfs_util.o(i.lfs_crc) for lfs_crc
    lfs.o(i.lfs_dir_compact) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_dir_compact) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_dir_compact) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    lfs.o(i.lfs_dir_compact) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_dir_compact) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_dir_compact) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_traverse) for lfs_dir_traverse
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_split) for lfs_dir_split
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_fs_size) for lfs_fs_size
    lfs.o(i.lfs_dir_compact) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_getgstate) for lfs_dir_getgstate
    lfs.o(i.lfs_dir_compact) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_bd_erase) for lfs_bd_erase
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commitprog) for lfs_dir_commitprog
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commitattr) for lfs_dir_commitattr
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_iszero) for lfs_gstate_iszero
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_tole32) for lfs_gstate_tole32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_fromle32) for lfs_gstate_fromle32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commitcrc) for lfs_dir_commitcrc
    lfs.o(i.lfs_dir_compact) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_pair_swap) for lfs_pair_swap
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_xormove) for lfs_gstate_xormove
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_cache_drop) for lfs_cache_drop
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_alloc) for lfs_alloc
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_fs_relocate) for lfs_fs_relocate
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commit_size) for lfs_dir_commit_size
    lfs.o(i.lfs_dir_compact) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commit_commit) for lfs_dir_commit_commit
    lfs.o(i.lfs_dir_drop) refers to lfs.o(i.lfs_dir_getgstate) for lfs_dir_getgstate
    lfs.o(i.lfs_dir_drop) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_dir_drop) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_dir_drop) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_fetch) refers to lfs.o(i.lfs_dir_fetchmatch) for lfs_dir_fetchmatch
    lfs.o(i.lfs_dir_fetchmatch) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_dir_fetchmatch) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_dir_fetchmatch) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_dir_fetchmatch) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_dir_fetchmatch) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs_util.o(i.lfs_crc) for lfs_crc
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tag_isvalid) for lfs_tag_isvalid
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tag_type1) for lfs_tag_type1
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tag_chunk) for lfs_tag_chunk
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tag_splice) for lfs_tag_splice
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_pair_swap) for lfs_pair_swap
    lfs.o(i.lfs_dir_fetchmatch) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_dir_find) refers to strspn.o(.text) for strspn
    lfs.o(i.lfs_dir_find) refers to strcspn.o(.text) for strcspn
    lfs.o(i.lfs_dir_find) refers to memcmp.o(.text) for memcmp
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_find) refers to strchr.o(.text) for strchr
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_dir_fetchmatch) for lfs_dir_fetchmatch
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_dir_find_match) for lfs_dir_find_match
    lfs.o(i.lfs_dir_find_match) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_dir_find_match) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_find_match) refers to lfs.o(i.lfs_bd_cmp) for lfs_bd_cmp
    lfs.o(i.lfs_dir_get) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_dir_get) refers to lfs.o(i.lfs_dir_getslice) for lfs_dir_getslice
    lfs.o(i.lfs_dir_getgstate) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_dir_getgstate) refers to lfs.o(i.lfs_gstate_fromle32) for lfs_gstate_fromle32
    lfs.o(i.lfs_dir_getinfo) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_dir_getinfo) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_dir_getinfo) refers to lfs.o(i.lfs_ctz_fromle32) for lfs_ctz_fromle32
    lfs.o(i.lfs_dir_getinfo) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_dir_getread) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_getread) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    lfs.o(i.lfs_dir_getread) refers to lfs.o(i.lfs_aligndown) for lfs_aligndown
    lfs.o(i.lfs_dir_getread) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_dir_getread) refers to lfs.o(i.lfs_dir_getslice) for lfs_dir_getslice
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_tag_type1) for lfs_tag_type1
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_tag_splice) for lfs_tag_splice
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_tag_isdelete) for lfs_tag_isdelete
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_getslice) refers to rt_memclr.o(.text) for __aeabi_memclr
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_read) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_dir_read) refers to strcpy.o(.text) for strcpy
    lfs.o(i.lfs_dir_read) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_read) refers to lfs.o(i.lfs_dir_getinfo) for lfs_dir_getinfo
    lfs.o(i.lfs_dir_rewind) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_seek) refers to lfs.o(i.lfs_dir_rewind) for lfs_dir_rewind
    lfs.o(i.lfs_dir_seek) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_seek) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_split) refers to lfs.o(i.lfs_dir_alloc) for lfs_dir_alloc
    lfs.o(i.lfs_dir_split) refers to lfs.o(i.lfs_dir_compact) for lfs_dir_compact
    lfs.o(i.lfs_dir_split) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_dir_traverse_filter) for lfs_dir_traverse_filter
    lfs.o(i.lfs_dir_traverse_filter) refers to lfs.o(i.lfs_tag_isdelete) for lfs_tag_isdelete
    lfs.o(i.lfs_dir_traverse_filter) refers to lfs.o(i.lfs_tag_type1) for lfs_tag_type1
    lfs.o(i.lfs_dir_traverse_filter) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_dir_traverse_filter) refers to lfs.o(i.lfs_tag_splice) for lfs_tag_splice
    lfs.o(i.lfs_file_close) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_close) refers to lfs.o(i.lfs_file_sync) for lfs_file_sync
    lfs.o(i.lfs_file_close) refers to lfs.o(i.lfs_free) for lfs_free
    lfs.o(i.lfs_file_flush) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_file_flush) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_file_flush) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_file_flush) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_file_flush) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_file_flush) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_cache_drop) for lfs_cache_drop
    lfs.o(i.lfs_file_flush) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_file_read) for lfs_file_read
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_file_write) for lfs_file_write
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_bd_flush) for lfs_bd_flush
    lfs.o(i.lfs_file_flush) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_file_relocate) for lfs_file_relocate
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_max) for lfs_max
    lfs.o(i.lfs_file_flush) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_file_open) refers to lfs.o(i.lfs_file_opencfg) for lfs_file_opencfg
    lfs.o(i.lfs_file_open) refers to lfs.o(.constdata) for defaults
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_fs_forceconsistency) for lfs_fs_forceconsistency
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_file_opencfg) refers to strlen.o(.text) for strlen
    lfs.o(i.lfs_file_opencfg) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_ctz_fromle32) for lfs_ctz_fromle32
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_malloc) for lfs_malloc
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_file_close) for lfs_file_close
    lfs.o(i.lfs_file_outline) refers to lfs.o(i.lfs_alloc_ack) for lfs_alloc_ack
    lfs.o(i.lfs_file_outline) refers to lfs.o(i.lfs_file_relocate) for lfs_file_relocate
    lfs.o(i.lfs_file_read) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_read) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_read) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_file_read) refers to lfs.o(i.lfs_ctz_find) for lfs_ctz_find
    lfs.o(i.lfs_file_read) refers to lfs.o(i.lfs_dir_getread) for lfs_dir_getread
    lfs.o(i.lfs_file_read) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_file_relocate) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_file_relocate) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_file_relocate) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_file_relocate) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_file_relocate) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_file_relocate) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_alloc) for lfs_alloc
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_bd_erase) for lfs_bd_erase
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_dir_getread) for lfs_dir_getread
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_file_relocate) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_file_relocate) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_cache_drop) for lfs_cache_drop
    lfs.o(i.lfs_file_rewind) refers to lfs.o(i.lfs_file_seek) for lfs_file_seek
    lfs.o(i.lfs_file_seek) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_seek) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_size) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_size) refers to lfs.o(i.lfs_max) for lfs_max
    lfs.o(i.lfs_file_sync) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_ctz_tole32) for lfs_ctz_tole32
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_file_outline) for lfs_file_outline
    lfs.o(i.lfs_file_tell) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_truncate) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_file_size) for lfs_file_size
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_ctz_find) for lfs_ctz_find
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_file_seek) for lfs_file_seek
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_file_write) for lfs_file_write
    lfs.o(i.lfs_file_write) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_max) for lfs_max
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_file_outline) for lfs_file_outline
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_ctz_find) for lfs_ctz_find
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_alloc_ack) for lfs_alloc_ack
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_ctz_extend) for lfs_ctz_extend
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_file_relocate) for lfs_file_relocate
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_init) for lfs_init
    lfs.o(i.lfs_format) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_alloc_ack) for lfs_alloc_ack
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_dir_alloc) for lfs_dir_alloc
    lfs.o(i.lfs_format) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_superblock_tole32) for lfs_superblock_tole32
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_deinit) for lfs_deinit
    lfs.o(i.lfs_format) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_free) refers to h1_free.o(.text) for free
    lfs.o(i.lfs_fs_demove) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_fs_demove) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_fs_demove) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_fs_demove) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_fs_demove) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_fs_demove) refers to lfs.o(i.lfs_gstate_hasmove) for lfs_gstate_hasmove
    lfs.o(i.lfs_fs_demove) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_fs_demove) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_fs_demove) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_fs_demove) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_fs_deorphan) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_fs_deorphan) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_fs_deorphan) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_fs_deorphan) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_fs_deorphan) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_gstate_hasorphans) for lfs_gstate_hasorphans
    lfs.o(i.lfs_fs_deorphan) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_fs_parent) for lfs_fs_parent
    lfs.o(i.lfs_fs_deorphan) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_dir_drop) for lfs_dir_drop
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_fs_forceconsistency) refers to lfs.o(i.lfs_fs_demove) for lfs_fs_demove
    lfs.o(i.lfs_fs_forceconsistency) refers to lfs.o(i.lfs_fs_deorphan) for lfs_fs_deorphan
    lfs.o(i.lfs_fs_parent) refers to lfs.o(i.lfs_dir_fetchmatch) for lfs_dir_fetchmatch
    lfs.o(i.lfs_fs_parent) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_fs_parent) refers to lfs.o(i.lfs_fs_parent_match) for lfs_fs_parent_match
    lfs.o(i.lfs_fs_parent_match) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_fs_parent_match) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_fs_parent_match) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_fs_pred) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_fs_pred) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_fs_pred) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_fs_prepmove) refers to lfs.o(i.lfs_gstate_xormove) for lfs_gstate_xormove
    lfs.o(i.lfs_fs_preporphans) refers to lfs.o(i.lfs_gstate_hasorphans) for lfs_gstate_hasorphans
    lfs.o(i.lfs_fs_preporphans) refers to lfs.o(i.lfs_gstate_xororphans) for lfs_gstate_xororphans
    lfs.o(i.lfs_fs_relocate) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_fs_relocate) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_fs_relocate) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_fs_relocate) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_fs_relocate) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_fs_relocate) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_fs_parent) for lfs_fs_parent
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_fs_pred) for lfs_fs_pred
    lfs.o(i.lfs_fs_size) refers to lfs.o(i.lfs_fs_traverse) for lfs_fs_traverse
    lfs.o(i.lfs_fs_size) refers to lfs.o(i.lfs_fs_size_count) for lfs_fs_size_count
    lfs.o(i.lfs_fs_traverse) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_ctz_fromle32) for lfs_ctz_fromle32
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_ctz_traverse) for lfs_ctz_traverse
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_gstate_fromle32) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_gstate_hasmove) refers to lfs.o(i.lfs_tag_type1) for lfs_tag_type1
    lfs.o(i.lfs_gstate_hasmovehere) refers to lfs.o(i.lfs_tag_type1) for lfs_tag_type1
    lfs.o(i.lfs_gstate_hasmovehere) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_gstate_hasorphans) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_gstate_tole32) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_init) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_init) refers to lfs.o(i.lfs_npw2) for lfs_npw2
    lfs.o(i.lfs_init) refers to lfs.o(i.lfs_malloc) for lfs_malloc
    lfs.o(i.lfs_init) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_init) refers to lfs.o(i.lfs_deinit) for lfs_deinit
    lfs.o(i.lfs_init) refers to lfs.o(.conststring) for .conststring
    lfs.o(i.lfs_init) refers to lfs.o(.constdata) for <Data3>
    lfs.o(i.lfs_malloc) refers to h1_alloc.o(.text) for malloc
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_fs_forceconsistency) for lfs_fs_forceconsistency
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_mkdir) refers to strlen.o(.text) for strlen
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_alloc_ack) for lfs_alloc_ack
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_dir_alloc) for lfs_dir_alloc
    lfs.o(i.lfs_mkdir) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_mkdir) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_mkdir) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_mount) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_mount) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_mount) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_mount) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    lfs.o(i.lfs_mount) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_mount) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_init) for lfs_init
    lfs.o(i.lfs_mount) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_dir_fetchmatch) for lfs_dir_fetchmatch
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_tag_isdelete) for lfs_tag_isdelete
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_superblock_fromle32) for lfs_superblock_fromle32
    lfs.o(i.lfs_mount) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_dir_getgstate) for lfs_dir_getgstate
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_tag_isvalid) for lfs_tag_isvalid
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_gstate_hasmove) for lfs_gstate_hasmove
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_alloc_ack) for lfs_alloc_ack
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_unmount) for lfs_unmount
    lfs.o(i.lfs_mount) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_dir_find_match) for lfs_dir_find_match
    lfs.o(i.lfs_pair_fromle32) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_pair_tole32) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_popc) refers to lfs.o(i.__ARM_pop) for __ARM_pop
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_fs_forceconsistency) for lfs_fs_forceconsistency
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_fs_pred) for lfs_fs_pred
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_drop) for lfs_dir_drop
    lfs.o(i.lfs_removeattr) refers to lfs.o(i.lfs_commitattr) for lfs_commitattr
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_fs_forceconsistency) for lfs_fs_forceconsistency
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_rename) refers to strlen.o(.text) for strlen
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_fs_prepmove) for lfs_fs_prepmove
    lfs.o(i.lfs_rename) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_fs_pred) for lfs_fs_pred
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_drop) for lfs_dir_drop
    lfs.o(i.lfs_setattr) refers to lfs.o(i.lfs_commitattr) for lfs_commitattr
    lfs.o(i.lfs_stat) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_stat) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_stat) refers to lfs.o(i.lfs_dir_getinfo) for lfs_dir_getinfo
    lfs.o(i.lfs_superblock_fromle32) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_superblock_tole32) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_tag_dsize) refers to lfs.o(i.lfs_tag_isdelete) for lfs_tag_isdelete
    lfs.o(i.lfs_tag_dsize) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_tag_splice) refers to lfs.o(i.lfs_tag_chunk) for lfs_tag_chunk
    lfs.o(i.lfs_tobe32) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_tole32) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_unmount) refers to lfs.o(i.lfs_deinit) for lfs_deinit
    lfs.o(.constdata) refers to lfs.o(.conststring) for .conststring
    lfs_port.o(i.lfs_deskio_erase) refers to gd25qxx.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    lfs_port.o(i.lfs_deskio_prog) refers to gd25qxx.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    lfs_port.o(i.lfs_deskio_read) refers to gd25qxx.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(i.lfs_deskio_read) for lfs_deskio_read
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(i.lfs_deskio_prog) for lfs_deskio_prog
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(i.lfs_deskio_erase) for lfs_deskio_erase
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(i.lfs_deskio_sync) for lfs_deskio_sync
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(.bss) for lfs_read_buffer
    lfs_util.o(i.lfs_crc) refers to lfs_util.o(.constdata) for rtable
    oled.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.I2C_Bus_Reset) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.I2C_Bus_Reset) refers to perf_counter.o(i.delay_ms) for delay_ms
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_deinit) for i2c_deinit
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_clock_config) for i2c_clock_config
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_mode_addr_config) for i2c_mode_addr_config
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_enable) for i2c_enable
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_ack_config) for i2c_ack_config
    oled.o(i.OLED_Allfill) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Clear) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Display_Off) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Init) refers to perf_counter.o(i.delay_ms) for delay_ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(.data) for initcmd1
    oled.o(i.OLED_Pow) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Set_Position) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for F8X16
    oled.o(i.OLED_ShowFloat) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowHanzi) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for Hzk
    oled.o(i.OLED_ShowHzbig) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for Hzb
    oled.o(i.OLED_ShowNum) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowStr) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Write_cmd) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_flag_get) for i2c_flag_get
    oled.o(i.OLED_Write_cmd) refers to oled.o(i.I2C_Bus_Reset) for I2C_Bus_Reset
    oled.o(i.OLED_Write_cmd) refers to perf_counter.o(i.delay_ms) for delay_ms
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_start_on_bus) for i2c_start_on_bus
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_master_addressing) for i2c_master_addressing
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_flag_clear) for i2c_flag_clear
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_memory_address_config) for dma_memory_address_config
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_transfer_number_config) for dma_transfer_number_config
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_dma_config) for i2c_dma_config
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_stop_on_bus) for i2c_stop_on_bus
    oled.o(i.OLED_Write_cmd) refers to mcu_cmic_gd32f470vet6.o(.data) for oled_cmd_buf
    oled.o(i.OLED_Write_data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_flag_get) for i2c_flag_get
    oled.o(i.OLED_Write_data) refers to oled.o(i.I2C_Bus_Reset) for I2C_Bus_Reset
    oled.o(i.OLED_Write_data) refers to perf_counter.o(i.delay_ms) for delay_ms
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_start_on_bus) for i2c_start_on_bus
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_master_addressing) for i2c_master_addressing
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_flag_clear) for i2c_flag_clear
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_memory_address_config) for dma_memory_address_config
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_transfer_number_config) for dma_transfer_number_config
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_dma_config) for i2c_dma_config
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_stop_on_bus) for i2c_stop_on_bus
    oled.o(i.OLED_Write_data) refers to mcu_cmic_gd32f470vet6.o(.data) for oled_data_buf
    oled.o(.constdata) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    ebtn.o(i.bit_array_cmp) refers to memcmp.o(.text) for memcmp
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_combo_btn_add_btn_by_idx) for ebtn_combo_btn_add_btn_by_idx
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx) for ebtn_combo_btn_remove_btn_by_idx
    ebtn.o(i.ebtn_combo_register) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_btn_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_btn_index_by_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_btn_dyn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_config) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_current_state) refers to ebtn.o(i.bit_array_assign) for bit_array_assign
    ebtn.o(i.ebtn_get_current_state) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_total_btn_cnt) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_init) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(i.ebtn_is_btn_in_process) for ebtn_is_btn_in_process
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_get_current_state) for ebtn_get_current_state
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_process_with_curr_state) for ebtn_process_with_curr_state
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_num_bits_set) for bit_array_num_bits_set
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_or) for bit_array_or
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn) for ebtn_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn_combo) for ebtn_process_btn_combo
    ebtn.o(i.ebtn_process_with_curr_state) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_register) refers to ebtn.o(i.ebtn_get_total_btn_cnt) for ebtn_get_total_btn_cnt
    ebtn.o(i.ebtn_register) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_set_combo_suppress_threshold) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_set_config) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.prv_get_combo_btn_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.ebtn_timer_sub) for ebtn_timer_sub
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.prv_get_combo_btn_by_key_id) for prv_get_combo_btn_by_key_id
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.prv_process_btn) refers to ebtn.o(.bss) for ebtn_default
    sdio_sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.r1_error_check) refers to sdio_sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdio_sdcard.o(i.r2_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r3_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.r7_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.rcu_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.dma_receive_config) for dma_receive_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_bus_mode_config) refers to sdio_sdcard.o(i.sd_bus_width_config) for sd_bus_width_config
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_bus_mode_config) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(i.sd_scr_get) for sd_scr_get
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(.data) for sd_scr
    sdio_sdcard.o(i.sd_card_capacity_get) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_capacity_get) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_card_information_get) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_information_get) refers to sdio_sdcard.o(.bss) for sd_cid
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_power_state_get) for sdio_power_state_get
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(i.r2_error_check) for r2_error_check
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(i.r6_error_check) for r6_error_check
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(.bss) for sd_cid
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_select_deselect) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_card_state_get) refers to sdio_sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdio_sdcard.o(i.sd_card_state_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_cardstatus_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_cardstatus_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.rcu_config) for rcu_config
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.gpio_config) for gpio_config
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_deinit) for sdio_deinit
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.sd_power_on) for sd_power_on
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.sd_card_init) for sd_card_init
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_get) for sdio_interrupt_flag_get
    sdio_sdcard.o(i.sd_interrupts_process) refers to sdio_sdcard.o(i.sd_transfer_stop) for sd_transfer_stop
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear) for sdio_interrupt_flag_clear
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_disable) for sdio_interrupt_disable
    sdio_sdcard.o(i.sd_interrupts_process) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.dma_receive_config) for dma_receive_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_power_off) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_enable) for sdio_clock_enable
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.cmdsent_error_check) for cmdsent_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r7_error_check) for r7_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r3_error_check) for r3_error_check
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_scr_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_sdstatus_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_sdstatus_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_transfer_mode_config) refers to sdio_sdcard.o(.data) for transmode
    sdio_sdcard.o(i.sd_transfer_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_transfer_stop) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    ff.o(i.check_fs) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_status) for disk_status
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.chk_mounted) refers to ff.o(i.check_fs) for check_fs
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.chk_mounted) refers to ff.o(.data) for FatFs
    ff.o(i.cmp_lfn) refers to fatfs_unicode.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.cmp_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.create_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.create_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.create_name) refers to fatfs_unicode.o(i.ff_convert) for ff_convert
    ff.o(i.create_name) refers to ff.o(i.chk_chr) for chk_chr
    ff.o(i.create_name) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.create_name) refers to fatfs_unicode.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.dir_find) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_find) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_find) refers to ff.o(i.cmp_lfn) for cmp_lfn
    ff.o(i.dir_find) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_find) refers to ff.o(i.mem_cmp) for mem_cmp
    ff.o(i.dir_find) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_next) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_next) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.dir_next) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_next) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_next) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.dir_read) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_read) refers to ff.o(i.pick_lfn) for pick_lfn
    ff.o(i.dir_read) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_read) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.dir_register) refers to ff.o(i.gen_numname) for gen_numname
    ff.o(i.dir_register) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.dir_register) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_register) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_register) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_register) refers to ff.o(i.fit_lfn) for fit_lfn
    ff.o(i.dir_register) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_remove) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_remove) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_remove) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_sdi) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_sdi) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_chmod) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_chmod) refers to fatfs_unicode.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_chmod) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_chmod) refers to fatfs_unicode.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_chmod) refers to ff.o(i.sync) for sync
    ff.o(i.f_close) refers to ff.o(i.f_sync) for f_sync
    ff.o(i.f_getfree) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_getfree) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_getfree) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_lseek) refers to ff.o(i.validate) for validate
    ff.o(i.f_lseek) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_lseek) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_lseek) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_lseek) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_lseek) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_mkdir) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkdir) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_mkdir) refers to fatfs_unicode.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_mkdir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_mkdir) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_mkdir) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_mkdir) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkdir) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkdir) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_mkdir) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_mkdir) refers to ff.o(i.sync) for sync
    ff.o(i.f_mkdir) refers to fatfs_unicode.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_mount) refers to ff.o(.data) for FatFs
    ff.o(i.f_open) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_open) refers to fatfs_unicode.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_open) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_open) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_open) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_open) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_open) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_open) refers to fatfs_unicode.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_opendir) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_opendir) refers to fatfs_unicode.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_opendir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_opendir) refers to fatfs_unicode.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_opendir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_read) refers to ff.o(i.validate) for validate
    ff.o(i.f_read) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_read) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_read) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_read) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_read) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_readdir) refers to ff.o(i.validate) for validate
    ff.o(i.f_readdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_readdir) refers to fatfs_unicode.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_readdir) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_readdir) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_readdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.f_readdir) refers to fatfs_unicode.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_rename) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_rename) refers to fatfs_unicode.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_rename) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_rename) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_rename) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_rename) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_rename) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_rename) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_rename) refers to ff.o(i.sync) for sync
    ff.o(i.f_rename) refers to fatfs_unicode.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_stat) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_stat) refers to fatfs_unicode.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_stat) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_stat) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_stat) refers to fatfs_unicode.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_sync) refers to ff.o(i.validate) for validate
    ff.o(i.f_sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_sync) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_sync) refers to ff.o(i.sync) for sync
    ff.o(i.f_truncate) refers to ff.o(i.validate) for validate
    ff.o(i.f_truncate) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_truncate) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_truncate) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.f_unlink) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_unlink) refers to fatfs_unicode.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_unlink) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_unlink) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_unlink) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_unlink) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_unlink) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_unlink) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_unlink) refers to ff.o(i.sync) for sync
    ff.o(i.f_unlink) refers to fatfs_unicode.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_utime) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_utime) refers to fatfs_unicode.o(i.ff_memalloc) for ff_memalloc
    ff.o(i.f_utime) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_utime) refers to fatfs_unicode.o(i.ff_memfree) for ff_memfree
    ff.o(i.f_utime) refers to ff.o(i.sync) for sync
    ff.o(i.f_write) refers to ff.o(i.validate) for validate
    ff.o(i.f_write) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_write) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_write) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_write) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_write) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.fit_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.follow_path) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.follow_path) refers to ff.o(i.create_name) for create_name
    ff.o(i.follow_path) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.gen_numname) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.get_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.get_fileinfo) refers to fatfs_unicode.o(i.ff_convert) for ff_convert
    ff.o(i.move_window) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.move_window) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.pick_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.put_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.remove_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.remove_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.sync) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.sync) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.validate) refers to diskio.o(i.disk_status) for disk_status
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_init) for sd_init
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_card_information_get) for sd_card_information_get
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_card_select_deselect) for sd_card_select_deselect
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_cardstatus_get) for sd_cardstatus_get
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_bus_mode_config) for sd_bus_mode_config
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_transfer_mode_config) for sd_transfer_mode_config
    diskio.o(i.disk_read) refers to sdio_sdcard.o(i.sd_block_read) for sd_block_read
    diskio.o(i.disk_read) refers to sdio_sdcard.o(i.sd_multiblocks_read) for sd_multiblocks_read
    diskio.o(i.disk_write) refers to sdio_sdcard.o(i.sd_block_write) for sd_block_write
    diskio.o(i.disk_write) refers to sdio_sdcard.o(i.sd_multiblocks_write) for sd_multiblocks_write
    diskio.o(i.get_fattime) refers to perf_counter.o(i.get_system_ms) for get_system_ms
    fatfs_unicode.o(i.ff_memalloc) refers to h1_alloc.o(.text) for malloc
    fatfs_unicode.o(i.ff_memfree) refers to h1_free.o(.text) for free
    fatfs_unicode.o(i.ff_wtoupper) refers to fatfs_unicode.o(.constdata) for lower_to_upper_table
    btn_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(i.app_btn_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(i.app_btn_init) refers to ebtn.o(i.ebtn_init) for ebtn_init
    btn_app.o(i.app_btn_init) refers to btn_app.o(i.prv_btn_event) for prv_btn_event
    btn_app.o(i.app_btn_init) refers to btn_app.o(i.prv_btn_get_state) for prv_btn_get_state
    btn_app.o(i.app_btn_init) refers to btn_app.o(.data) for btns
    btn_app.o(i.btn_task) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(i.btn_task) refers to perf_counter.o(i.get_system_ms) for get_system_ms
    btn_app.o(i.btn_task) refers to ebtn.o(i.ebtn_process) for ebtn_process
    btn_app.o(i.prv_btn_event) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(i.prv_btn_event) refers to sampling_app.o(i.sampling_toggle) for sampling_toggle
    btn_app.o(i.prv_btn_get_state) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(i.prv_btn_get_state) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    btn_app.o(.constdata) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(.data) refers to btn_app.o(.constdata) for defaul_ebtn_param
    led_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    led_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    led_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    led_app.o(i.led_disp) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    led_app.o(i.led_disp) refers to led_app.o(.data) for temp_old
    led_app.o(i.led_task) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    led_app.o(i.led_task) refers to led_app.o(i.led_disp) for led_disp
    led_app.o(i.led_task) refers to led_app.o(.data) for ucLed
    led_app.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled_app.o(i.oled_printf) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled_app.o(i.oled_printf) refers to vsnprintf.o(.text) for vsnprintf
    oled_app.o(i.oled_printf) refers to oled.o(i.OLED_ShowStr) for OLED_ShowStr
    oled_app.o(i.oled_task) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled_app.o(i.oled_task) refers to _printf_pad.o(.text) for _printf_pre_padding
    oled_app.o(i.oled_task) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_app.o(i.oled_task) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    oled_app.o(i.oled_task) refers to _printf_dec.o(.text) for _printf_int_dec
    oled_app.o(i.oled_task) refers to oled_app.o(i.oled_printf) for oled_printf
    oled_app.o(i.oled_task) refers to __2sprintf.o(.text) for __2sprintf
    oled_app.o(i.oled_task) refers to sampling_app.o(i.adc_to_voltage) for adc_to_voltage
    oled_app.o(i.oled_task) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    oled_app.o(i.oled_task) refers to sampling_app.o(.data) for sampling_state
    oled_app.o(i.oled_task) refers to rtc_app.o(.data) for current_time
    oled_app.o(i.oled_task) refers to mcu_cmic_gd32f470vet6.o(.data) for adc_value
    scheduler.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    scheduler.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    scheduler.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    scheduler.o(i.scheduler_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for task_num
    scheduler.o(i.scheduler_run) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    scheduler.o(i.scheduler_run) refers to perf_counter.o(i.get_system_ms) for get_system_ms
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for scheduler_task
    scheduler.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    scheduler.o(.data) refers to led_app.o(i.led_task) for led_task
    scheduler.o(.data) refers to adc_app.o(i.adc_task) for adc_task
    scheduler.o(.data) refers to oled_app.o(i.oled_task) for oled_task
    scheduler.o(.data) refers to btn_app.o(i.btn_task) for btn_task
    scheduler.o(.data) refers to usart_app.o(i.uart_task) for uart_task
    scheduler.o(.data) refers to rtc_app.o(i.rtc_task) for rtc_task
    scheduler.o(.data) refers to sampling_app.o(i.sampling_task) for sampling_task
    usart_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    usart_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    usart_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    usart_app.o(i.my_printf) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    usart_app.o(i.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    usart_app.o(i.my_printf) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    usart_app.o(i.my_printf) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart_app.o(i.my_printf) refers to usart_app.o(.data) for tx_count
    usart_app.o(i.parse_datetime) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    usart_app.o(i.parse_datetime) refers to _scanf_int.o(.text) for _scanf_int
    usart_app.o(i.parse_datetime) refers to __0sscanf.o(.text) for __0sscanf
    usart_app.o(i.uart_task) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    usart_app.o(i.uart_task) refers to strlen.o(.text) for strlen
    usart_app.o(i.uart_task) refers to rt_memclr.o(.text) for __aeabi_memclr
    usart_app.o(i.uart_task) refers to config_app.o(i.config_process_input) for config_process_input
    usart_app.o(i.uart_task) refers to strncmp.o(.text) for strncmp
    usart_app.o(i.uart_task) refers to selftest_app.o(i.system_selftest) for system_selftest
    usart_app.o(i.uart_task) refers to sampling_app.o(i.sampling_start) for sampling_start
    usart_app.o(i.uart_task) refers to sampling_app.o(i.sampling_stop) for sampling_stop
    usart_app.o(i.uart_task) refers to config_app.o(i.config_cmd_handler) for config_cmd_handler
    usart_app.o(i.uart_task) refers to config_app.o(i.config_ratio_handler) for config_ratio_handler
    usart_app.o(i.uart_task) refers to config_app.o(i.config_limit_handler) for config_limit_handler
    usart_app.o(i.uart_task) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.uart_task) refers to rtc_app.o(i.rtc_get_time_string) for rtc_get_time_string
    usart_app.o(i.uart_task) refers to usart_app.o(i.parse_datetime) for parse_datetime
    usart_app.o(i.uart_task) refers to rtc_app.o(i.rtc_set_time) for rtc_set_time
    usart_app.o(i.uart_task) refers to usart_app.o(.data) for rx_flag
    usart_app.o(i.uart_task) refers to usart_app.o(.bss) for uart_dma_buffer
    usart_app.o(i.uart_task) refers to config_app.o(.data) for config_state
    usart_app.o(.bss) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    usart_app.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(i.card_info_get) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(i.card_info_get) refers to sdio_sdcard.o(i.sd_card_information_get) for sd_card_information_get
    sd_app.o(i.card_info_get) refers to usart_app.o(i.my_printf) for my_printf
    sd_app.o(i.card_info_get) refers to sdio_sdcard.o(i.sd_card_capacity_get) for sd_card_capacity_get
    sd_app.o(i.card_info_get) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    sd_app.o(i.memory_compare) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(i.sd_fatfs_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(i.sd_fatfs_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    sd_app.o(i.sd_fatfs_test) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(i.sd_fatfs_test) refers to diskio.o(i.disk_initialize) for disk_initialize
    sd_app.o(i.sd_fatfs_test) refers to sd_app.o(i.card_info_get) for card_info_get
    sd_app.o(i.sd_fatfs_test) refers to usart_app.o(i.my_printf) for my_printf
    sd_app.o(i.sd_fatfs_test) refers to ff.o(i.f_mount) for f_mount
    sd_app.o(i.sd_fatfs_test) refers to ff.o(i.f_open) for f_open
    sd_app.o(i.sd_fatfs_test) refers to __2sprintf.o(.text) for __2sprintf
    sd_app.o(i.sd_fatfs_test) refers to ff.o(i.f_write) for f_write
    sd_app.o(i.sd_fatfs_test) refers to ff.o(i.f_close) for f_close
    sd_app.o(i.sd_fatfs_test) refers to ff.o(i.f_read) for f_read
    sd_app.o(i.sd_fatfs_test) refers to sd_app.o(i.memory_compare) for memory_compare
    sd_app.o(i.sd_fatfs_test) refers to sd_app.o(.bss) for fs
    sd_app.o(i.sd_fatfs_test) refers to sd_app.o(.data) for result
    sd_app.o(.bss) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    adc_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    adc_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    adc_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    adc_app.o(i.adc_task) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    adc_app.o(i.adc_task) refers to mcu_cmic_gd32f470vet6.o(.data) for adc_value
    rtc_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    rtc_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    rtc_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    rtc_app.o(i.rtc_app_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    rtc_app.o(i.rtc_app_init) refers to rtc_app.o(i.rtc_set_time) for rtc_set_time
    rtc_app.o(i.rtc_app_init) refers to perf_counter.o(i.get_system_ms) for get_system_ms
    rtc_app.o(i.rtc_app_init) refers to rtc_app.o(.data) for rtc_uart_send_flag
    rtc_app.o(i.rtc_get_time_string) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    rtc_app.o(i.rtc_get_time_string) refers to _printf_pad.o(.text) for _printf_pre_padding
    rtc_app.o(i.rtc_get_time_string) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    rtc_app.o(i.rtc_get_time_string) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    rtc_app.o(i.rtc_get_time_string) refers to _printf_dec.o(.text) for _printf_int_dec
    rtc_app.o(i.rtc_get_time_string) refers to __2snprintf.o(.text) for __2snprintf
    rtc_app.o(i.rtc_get_time_string) refers to rtc_app.o(.data) for current_time
    rtc_app.o(i.rtc_set_time) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    rtc_app.o(i.rtc_set_time) refers to perf_counter.o(i.get_system_ms) for get_system_ms
    rtc_app.o(i.rtc_set_time) refers to rtc_app.o(.data) for current_time
    rtc_app.o(i.rtc_task) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    rtc_app.o(i.rtc_task) refers to perf_counter.o(i.get_system_ms) for get_system_ms
    rtc_app.o(i.rtc_task) refers to rtc_app.o(i.rtc_update_time) for rtc_update_time
    rtc_app.o(i.rtc_task) refers to rtc_app.o(i.rtc_get_time_string) for rtc_get_time_string
    rtc_app.o(i.rtc_task) refers to usart_app.o(i.my_printf) for my_printf
    rtc_app.o(i.rtc_task) refers to rtc_app.o(.data) for last_second_update
    rtc_app.o(i.rtc_update_time) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    rtc_app.o(i.rtc_update_time) refers to rtc_app.o(.data) for current_time
    rtc_app.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    selftest_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    selftest_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    selftest_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    selftest_app.o(i.flash_selftest) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    selftest_app.o(i.flash_selftest) refers to gd25qxx.o(i.spi_flash_read_id) for spi_flash_read_id
    selftest_app.o(i.get_flash_chip_name) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    selftest_app.o(i.get_flash_chip_name) refers to selftest_app.o(.constdata) for flash_chip_db
    selftest_app.o(i.system_selftest) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    selftest_app.o(i.system_selftest) refers to usart_app.o(i.my_printf) for my_printf
    selftest_app.o(i.system_selftest) refers to selftest_app.o(i.flash_selftest) for flash_selftest
    selftest_app.o(i.system_selftest) refers to selftest_app.o(i.tf_card_selftest) for tf_card_selftest
    selftest_app.o(i.system_selftest) refers to gd25qxx.o(i.spi_flash_read_id) for spi_flash_read_id
    selftest_app.o(i.system_selftest) refers to selftest_app.o(i.get_flash_chip_name) for get_flash_chip_name
    selftest_app.o(i.system_selftest) refers to sdio_sdcard.o(i.sd_card_capacity_get) for sd_card_capacity_get
    selftest_app.o(i.system_selftest) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    selftest_app.o(i.system_selftest) refers to rtc_app.o(i.rtc_get_time_string) for rtc_get_time_string
    selftest_app.o(i.tf_card_selftest) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    selftest_app.o(i.tf_card_selftest) refers to diskio.o(i.disk_initialize) for disk_initialize
    selftest_app.o(.constdata) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    selftest_app.o(.constdata) refers to selftest_app.o(.conststring) for .conststring
    selftest_app.o(.conststring) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sampling_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sampling_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sampling_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sampling_app.o(i.adc_to_voltage) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sampling_app.o(i.sampling_app_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sampling_app.o(i.sampling_app_init) refers to sampling_app.o(.data) for sampling_state
    sampling_app.o(i.sampling_start) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sampling_app.o(i.sampling_start) refers to perf_counter.o(i.get_system_ms) for get_system_ms
    sampling_app.o(i.sampling_start) refers to usart_app.o(i.my_printf) for my_printf
    sampling_app.o(i.sampling_start) refers to rtc_app.o(i.rtc_get_time_string) for rtc_get_time_string
    sampling_app.o(i.sampling_start) refers to sampling_app.o(i.adc_to_voltage) for adc_to_voltage
    sampling_app.o(i.sampling_start) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    sampling_app.o(i.sampling_start) refers to sampling_app.o(.data) for sampling_state
    sampling_app.o(i.sampling_start) refers to mcu_cmic_gd32f470vet6.o(.data) for adc_value
    sampling_app.o(i.sampling_stop) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sampling_app.o(i.sampling_stop) refers to usart_app.o(i.my_printf) for my_printf
    sampling_app.o(i.sampling_stop) refers to sampling_app.o(.data) for sampling_state
    sampling_app.o(i.sampling_task) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sampling_app.o(i.sampling_task) refers to perf_counter.o(i.get_system_ms) for get_system_ms
    sampling_app.o(i.sampling_task) refers to rtc_app.o(i.rtc_get_time_string) for rtc_get_time_string
    sampling_app.o(i.sampling_task) refers to sampling_app.o(i.adc_to_voltage) for adc_to_voltage
    sampling_app.o(i.sampling_task) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    sampling_app.o(i.sampling_task) refers to usart_app.o(i.my_printf) for my_printf
    sampling_app.o(i.sampling_task) refers to sampling_app.o(.data) for sampling_state
    sampling_app.o(i.sampling_task) refers to mcu_cmic_gd32f470vet6.o(.data) for adc_value
    sampling_app.o(i.sampling_toggle) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sampling_app.o(i.sampling_toggle) refers to sampling_app.o(i.sampling_stop) for sampling_stop
    sampling_app.o(i.sampling_toggle) refers to sampling_app.o(i.sampling_start) for sampling_start
    sampling_app.o(i.sampling_toggle) refers to sampling_app.o(.data) for sampling_state
    sampling_app.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    config_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    config_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    config_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    config_app.o(i.config_app_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    config_app.o(i.config_app_init) refers to config_app.o(.data) for config_params
    config_app.o(i.config_cmd_handler) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    config_app.o(i.config_cmd_handler) refers to config_app.o(i.config_read_from_tf) for config_read_from_tf
    config_app.o(i.config_cmd_handler) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    config_app.o(i.config_cmd_handler) refers to usart_app.o(i.my_printf) for my_printf
    config_app.o(i.config_cmd_handler) refers to config_app.o(.data) for config_params
    config_app.o(i.config_limit_handler) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    config_app.o(i.config_limit_handler) refers to usart_app.o(i.my_printf) for my_printf
    config_app.o(i.config_limit_handler) refers to config_app.o(.data) for config_params
    config_app.o(i.config_process_input) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    config_app.o(i.config_process_input) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    config_app.o(i.config_process_input) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    config_app.o(i.config_process_input) refers to config_app.o(i.config_set_ratio) for config_set_ratio
    config_app.o(i.config_process_input) refers to usart_app.o(i.my_printf) for my_printf
    config_app.o(i.config_process_input) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    config_app.o(i.config_process_input) refers to atoi.o(.text) for atoi
    config_app.o(i.config_process_input) refers to config_app.o(i.config_set_limit) for config_set_limit
    config_app.o(i.config_process_input) refers to config_app.o(.data) for config_state
    config_app.o(i.config_ratio_handler) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    config_app.o(i.config_ratio_handler) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    config_app.o(i.config_ratio_handler) refers to usart_app.o(i.my_printf) for my_printf
    config_app.o(i.config_ratio_handler) refers to config_app.o(.data) for config_params
    config_app.o(i.config_read_from_tf) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    config_app.o(i.config_read_from_tf) refers to ff.o(i.f_open) for f_open
    config_app.o(i.config_read_from_tf) refers to strchr.o(.text) for strchr
    config_app.o(i.config_read_from_tf) refers to strncmp.o(.text) for strncmp
    config_app.o(i.config_read_from_tf) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    config_app.o(i.config_read_from_tf) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    config_app.o(i.config_read_from_tf) refers to atoi.o(.text) for atoi
    config_app.o(i.config_read_from_tf) refers to ff.o(i.f_close) for f_close
    config_app.o(i.config_read_from_tf) refers to config_app.o(.data) for config_params
    config_app.o(i.config_set_limit) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    config_app.o(i.config_set_limit) refers to config_app.o(i.config_write_to_tf) for config_write_to_tf
    config_app.o(i.config_set_limit) refers to config_app.o(.data) for config_params
    config_app.o(i.config_set_ratio) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    config_app.o(i.config_set_ratio) refers to config_app.o(i.config_write_to_tf) for config_write_to_tf
    config_app.o(i.config_set_ratio) refers to config_app.o(.data) for config_params
    config_app.o(i.config_write_to_tf) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    config_app.o(i.config_write_to_tf) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    config_app.o(i.config_write_to_tf) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    config_app.o(i.config_write_to_tf) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    config_app.o(i.config_write_to_tf) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    config_app.o(i.config_write_to_tf) refers to _printf_dec.o(.text) for _printf_int_dec
    config_app.o(i.config_write_to_tf) refers to ff.o(i.f_mkdir) for f_mkdir
    config_app.o(i.config_write_to_tf) refers to ff.o(i.f_open) for f_open
    config_app.o(i.config_write_to_tf) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    config_app.o(i.config_write_to_tf) refers to __2sprintf.o(.text) for __2sprintf
    config_app.o(i.config_write_to_tf) refers to strlen.o(.text) for strlen
    config_app.o(i.config_write_to_tf) refers to ff.o(i.f_write) for f_write
    config_app.o(i.config_write_to_tf) refers to ff.o(i.f_close) for f_close
    config_app.o(i.config_write_to_tf) refers to config_app.o(.data) for config_params
    config_app.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    startup_gd32f450_470.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_gd32f450_470.o(RESET) refers to systick_wrapper_ual.o(.text) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.USART0_IRQHandler) for USART0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SDIO_IRQHandler) for SDIO_IRQHandler
    startup_gd32f450_470.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to __main.o(!!!main) for __main
    startup_gd32f450_470.o(.text) refers to startup_gd32f450_470.o(HEAP) for Heap_Mem
    startup_gd32f450_470.o(.text) refers to startup_gd32f450_470.o(STACK) for Stack_Mem
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i._soft_delay_) for _soft_delay_
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_240m_25m_hxtal) for system_clock_240m_25m_hxtal
    perf_counter.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.EventRecorderTimerGetCount) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.EventRecorderTimerGetCount) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.EventRecorderTimerGetFreq) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.EventRecorderTimerGetFreq) refers to perfc_port_default.o(i.perfc_port_get_system_timer_freq) for perfc_port_get_system_timer_freq
    perf_counter.o(i.EventRecorderTimerSetup) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__on_context_switch_in) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__on_context_switch_in) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.__on_context_switch_out) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__on_context_switch_out) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.__on_context_switch_out) refers to perf_counter.o(.data) for g_nOffset
    perf_counter.o(i.__perf_counter_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__perf_counter_init) refers to perf_counter.o(i.init_cycle_counter) for init_cycle_counter
    perf_counter.o(i.__perf_os_patch_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__perfc_is_time_out) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__perfc_is_time_out) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.__start_task_cycle_counter) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__start_task_cycle_counter) refers to perf_counter.o(i.get_rtos_task_cycle_info) for get_rtos_task_cycle_info
    perf_counter.o(i.__start_task_cycle_counter) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.__start_task_cycle_counter) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.__start_task_cycle_counter) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.__stop_task_cycle_counter) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__stop_task_cycle_counter) refers to perf_counter.o(i.get_rtos_task_cycle_info) for get_rtos_task_cycle_info
    perf_counter.o(i.__stop_task_cycle_counter) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.__stop_task_cycle_counter) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.__stop_task_cycle_counter) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.__stop_task_cycle_counter) refers to perf_counter.o(.data) for g_nOffset
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perfc_port_default.o(i.perfc_port_stop_system_timer_counting) for perfc_port_stop_system_timer_counting
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perfc_port_default.o(i.perfc_port_is_system_timer_ovf_pending) for perfc_port_is_system_timer_ovf_pending
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perfc_port_default.o(i.perfc_port_clear_system_timer_ovf_pending) for perfc_port_clear_system_timer_ovf_pending
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) for perfc_port_insert_to_system_timer_insert_ovf_handler
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perfc_port_default.o(i.perfc_port_clear_system_timer_counter) for perfc_port_clear_system_timer_counter
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perf_counter.o(.data) for s_lSystemClockCounts
    perf_counter.o(i.check_systick) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.check_systick) refers to perfc_port_default.o(i.perfc_port_get_system_timer_elapsed) for perfc_port_get_system_timer_elapsed
    perf_counter.o(i.check_systick) refers to perfc_port_default.o(i.perfc_port_is_system_timer_ovf_pending) for perfc_port_is_system_timer_ovf_pending
    perf_counter.o(i.check_systick) refers to perfc_port_default.o(i.perfc_port_get_system_timer_top) for perfc_port_get_system_timer_top
    perf_counter.o(i.clock) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.clock) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.delay_ms) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.delay_ms) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.delay_ms) refers to perf_counter.o(.data) for s_wMSUnit
    perf_counter.o(i.delay_us) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.delay_us) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.delay_us) refers to perf_counter.o(.data) for s_wUSUnit
    perf_counter.o(i.disable_task_cycle_info) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.disable_task_cycle_info) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.disable_task_cycle_info) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.enable_task_cycle_info) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.enable_task_cycle_info) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.enable_task_cycle_info) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.get_rtos_task_cycle_info) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.get_system_ms) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.get_system_ms) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.get_system_ms) refers to perf_counter.o(i.check_systick) for check_systick
    perf_counter.o(i.get_system_ms) refers to llsdiv.o(.text) for __aeabi_ldivmod
    perf_counter.o(i.get_system_ms) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.get_system_ms) refers to perf_counter.o(.data) for s_wMSResidule
    perf_counter.o(i.get_system_ticks) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.get_system_ticks) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.get_system_ticks) refers to perf_counter.o(i.check_systick) for check_systick
    perf_counter.o(i.get_system_ticks) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.get_system_ticks) refers to perf_counter.o(.data) for s_lSystemClockCounts
    perf_counter.o(i.get_system_us) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.get_system_us) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.get_system_us) refers to perf_counter.o(i.check_systick) for check_systick
    perf_counter.o(i.get_system_us) refers to llsdiv.o(.text) for __aeabi_ldivmod
    perf_counter.o(i.get_system_us) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.get_system_us) refers to perf_counter.o(.data) for s_wUSResidule
    perf_counter.o(i.init_cycle_counter) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.init_cycle_counter) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.init_cycle_counter) refers to perfc_port_default.o(i.perfc_port_init_system_timer) for perfc_port_init_system_timer
    perf_counter.o(i.init_cycle_counter) refers to perfc_port_default.o(i.perfc_port_clear_system_timer_ovf_pending) for perfc_port_clear_system_timer_ovf_pending
    perf_counter.o(i.init_cycle_counter) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.init_cycle_counter) refers to perf_counter.o(i.update_perf_counter) for update_perf_counter
    perf_counter.o(i.init_cycle_counter) refers to perf_counter.o(i.__perf_os_patch_init) for __perf_os_patch_init
    perf_counter.o(i.init_cycle_counter) refers to perf_counter.o(.data) for s_lSystemClockCounts
    perf_counter.o(i.init_task_cycle_counter) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.init_task_cycle_counter) refers to perf_counter.o(i.get_rtos_task_cycle_info) for get_rtos_task_cycle_info
    perf_counter.o(i.init_task_cycle_counter) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    perf_counter.o(i.init_task_cycle_counter) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.init_task_cycle_info) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.init_task_cycle_info) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    perf_counter.o(i.perfc_check_task_stack_canary_safe) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_check_task_stack_canary_safe) refers to perf_counter.o(i.get_rtos_task_cycle_info) for get_rtos_task_cycle_info
    perf_counter.o(i.perfc_convert_ms_to_ticks) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_convert_ms_to_ticks) refers to perf_counter.o(.data) for s_wMSUnit
    perf_counter.o(i.perfc_convert_ticks_to_ms) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_convert_ticks_to_ms) refers to llsdiv.o(.text) for __aeabi_ldivmod
    perf_counter.o(i.perfc_convert_ticks_to_ms) refers to perf_counter.o(.data) for s_wMSUnit
    perf_counter.o(i.perfc_convert_ticks_to_us) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_convert_ticks_to_us) refers to llsdiv.o(.text) for __aeabi_ldivmod
    perf_counter.o(i.perfc_convert_ticks_to_us) refers to perf_counter.o(.data) for s_wUSUnit
    perf_counter.o(i.perfc_convert_us_to_ticks) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_convert_us_to_ticks) refers to perf_counter.o(.data) for s_wUSUnit
    perf_counter.o(i.perfc_get_systimer_frequency) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_get_systimer_frequency) refers to perfc_port_default.o(i.perfc_port_get_system_timer_freq) for perfc_port_get_system_timer_freq
    perf_counter.o(i.perfc_port_disable_global_interrupt) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) refers to perfc_port_default.o(i.perfc_port_get_system_timer_top) for perfc_port_get_system_timer_top
    perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) refers to llsdiv.o(.text) for __aeabi_ldivmod
    perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) refers to perf_counter.o(.data) for s_lSystemClockCounts
    perf_counter.o(i.perfc_port_resume_global_interrupt) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.register_task_cycle_agent) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.register_task_cycle_agent) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.register_task_cycle_agent) refers to perf_counter.o(i.get_rtos_task_cycle_info) for get_rtos_task_cycle_info
    perf_counter.o(i.register_task_cycle_agent) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.resume_task_cycle_info) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.unregister_task_cycle_agent) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.unregister_task_cycle_agent) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.unregister_task_cycle_agent) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.update_perf_counter) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.update_perf_counter) refers to perfc_port_default.o(i.perfc_port_get_system_timer_freq) for perfc_port_get_system_timer_freq
    perf_counter.o(i.update_perf_counter) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.update_perf_counter) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.update_perf_counter) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.update_perf_counter) refers to perf_counter.o(.data) for s_wUSUnit
    perf_counter.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(.init_array) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(.init_array) refers to perf_counter.o(i.__perf_counter_init) for __perf_counter_init
    perf_counter.o(.init_array) refers to init_aeabi.o(.text) for __cpp_initialize__aeabi_
    perfc_port_default.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_clear_system_timer_counter) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_clear_system_timer_ovf_pending) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_get_system_timer_elapsed) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_get_system_timer_freq) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_get_system_timer_freq) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    perfc_port_default.o(i.perfc_port_get_system_timer_top) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_init_system_timer) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_is_system_timer_ovf_pending) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_stop_system_timer_counting) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    systick_wrapper_ual.o(.text) refers to perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) for perfc_port_insert_to_system_timer_insert_ovf_handler
    systick_wrapper_ual.o(.text) refers to gd32f4xx_it.o(i.SysTick_Handler) for $Super$$SysTick_Handler
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    llsdiv.o(.text) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    atoi.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    assert.o(.text) refers to assert_puts.o(.text) for __assert_puts
    assert.o(.text) refers to abort.o(.text) for abort
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    init_aeabi.o(.emb_text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.init_array) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.init_array) refers to init_aeabi.o(.text) for __cpp_initialize__aeabi_
    init_aeabi.o(.dummy_text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.ARM.exidx) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.ARM.exidx) refers to init_aeabi.o(.text) for .text
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    atof.o(i.__hardfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__softfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to main.o(i.fputc) for fputc
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    strtod.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    strtod.o(.text) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace.o(.text) for isspace
    strtol.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    abort.o(.text) refers to defsig_abrt_outer.o(.text) for __rt_SIGABRT
    abort.o(.text) refers (Weak) to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    abort.o(.text) refers to sys_exit.o(.text) for _sys_exit
    assert_puts.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000024) refers (Weak) to initio.o(.text) for _initio
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000031) refers (Weak) to init_aeabi.o(.text) for __cpp_initialize__aeabi_
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _scanf.o(.text) refers (Weak) to scanf1.o(x$fpl$scanf1) for _scanf_real
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    initio.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_2
    initio.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio.o(.text) refers to fopen.o(.text) for freopen
    initio.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio.o(.text) refers to h1_free.o(.text) for free
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    initio_locked.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio_locked.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_2
    initio_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio_locked.o(.text) refers to fopen.o(.text) for freopen
    initio_locked.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio_locked.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio_locked.o(.text) refers to h1_free.o(.text) for free
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    defsig_abrt_outer.o(.text) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig_abrt_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_abrt_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_io.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.text) refers to strlen.o(.text) for strlen
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_gd32f450_470.o(.text) for __user_initial_stackheap
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to fpconst.o(c$$dmax) for __dbl_max
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    streamlock.o(.data) refers (Special) to initio.o(.text) for _initio
    fopen.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen.o(.text) refers to fseek.o(.text) for _fseek
    fopen.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fclose.o(.text) refers to stdio.o(.text) for _fflush
    fclose.o(.text) refers to sys_io.o(.text) for _sys_close
    fclose.o(.text) refers to h1_free.o(.text) for free
    fclose.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen_locked.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen_locked.o(.text) refers to fseek.o(.text) for _fseek
    fopen_locked.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fopen_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_outer.o(.text) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig_rtred_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtred_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) refers (Weak) to initio.o(.text) for _terminateio
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000D) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    fseek.o(.text) refers to sys_io.o(.text) for _sys_istty
    fseek.o(.text) refers to ftell.o(.text) for _ftell_internal
    fseek.o(.text) refers to stdio.o(.text) for _seterr
    stdio.o(.text) refers to sys_io.o(.text) for _sys_seek
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dmax) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    narrow.o(i.__hardfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__hardfp___mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__hardfp___mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__hardfp___mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    ftell.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig.o(CL$$defsig) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    frexp.o(i.__hardfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__hardfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ldexp.o(i.__hardfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__hardfp_ldexp) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp.o(i.__hardfp_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__hardfp_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to _rserrno.o(.text) for __set_errno
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    ldexp_x.o(i.____softfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____softfp_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.____support_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____support_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.__ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.__ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_it.o(.rrx_text), (6 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.fputc), (36 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing systick.o(.rrx_text), (6 bytes).
    Removing mcu_cmic_gd32f470vet6.o(.rev16_text), (4 bytes).
    Removing mcu_cmic_gd32f470vet6.o(.revsh_text), (4 bytes).
    Removing mcu_cmic_gd32f470vet6.o(.rrx_text), (6 bytes).
    Removing gd25qxx.o(.rev16_text), (4 bytes).
    Removing gd25qxx.o(.revsh_text), (4 bytes).
    Removing gd25qxx.o(.rrx_text), (6 bytes).
    Removing gd25qxx.o(i.spi_flash_buffer_read), (84 bytes).
    Removing gd25qxx.o(i.spi_flash_buffer_write), (290 bytes).
    Removing gd25qxx.o(i.spi_flash_bulk_erase), (44 bytes).
    Removing gd25qxx.o(i.spi_flash_page_write), (92 bytes).
    Removing gd25qxx.o(i.spi_flash_sector_erase), (68 bytes).
    Removing gd25qxx.o(i.spi_flash_send_halfword_dma), (268 bytes).
    Removing gd25qxx.o(i.spi_flash_start_read_sequence), (48 bytes).
    Removing gd25qxx.o(i.spi_flash_transmit_receive_dma), (292 bytes).
    Removing gd25qxx.o(i.spi_flash_wait_for_dma_end), (44 bytes).
    Removing gd25qxx.o(i.spi_flash_wait_for_write_end), (56 bytes).
    Removing gd25qxx.o(i.spi_flash_write_enable), (36 bytes).
    Removing gd25qxx.o(i.test_spi_flash), (772 bytes).
    Removing lfs.o(i.lfs_aligndown), (14 bytes).
    Removing lfs.o(i.lfs_alignup), (18 bytes).
    Removing lfs.o(i.lfs_alloc), (268 bytes).
    Removing lfs.o(i.lfs_alloc_ack), (8 bytes).
    Removing lfs.o(i.lfs_alloc_lookahead), (66 bytes).
    Removing lfs.o(i.lfs_bd_cmp), (102 bytes).
    Removing lfs.o(i.lfs_bd_erase), (128 bytes).
    Removing lfs.o(i.lfs_bd_flush), (268 bytes).
    Removing lfs.o(i.lfs_bd_prog), (364 bytes).
    Removing lfs.o(i.lfs_bd_read), (476 bytes).
    Removing lfs.o(i.lfs_bd_sync), (112 bytes).
    Removing lfs.o(i.lfs_cache_drop), (8 bytes).
    Removing lfs.o(i.lfs_cache_zero), (26 bytes).
    Removing lfs.o(i.lfs_commitattr), (120 bytes).
    Removing lfs.o(i.lfs_ctz), (18 bytes).
    Removing lfs.o(i.lfs_ctz_extend), (600 bytes).
    Removing lfs.o(i.lfs_ctz_find), (272 bytes).
    Removing lfs.o(i.lfs_ctz_fromle32), (22 bytes).
    Removing lfs.o(i.lfs_ctz_index), (72 bytes).
    Removing lfs.o(i.lfs_ctz_tole32), (22 bytes).
    Removing lfs.o(i.lfs_ctz_traverse), (170 bytes).
    Removing lfs.o(i.lfs_deinit), (44 bytes).
    Removing lfs.o(i.lfs_dir_alloc), (152 bytes).
    Removing lfs.o(i.lfs_dir_close), (36 bytes).
    Removing lfs.o(i.lfs_dir_commit), (1060 bytes).
    Removing lfs.o(i.lfs_dir_commit_commit), (28 bytes).
    Removing lfs.o(i.lfs_dir_commit_size), (30 bytes).
    Removing lfs.o(i.lfs_dir_commitattr), (200 bytes).
    Removing lfs.o(i.lfs_dir_commitcrc), (444 bytes).
    Removing lfs.o(i.lfs_dir_commitprog), (74 bytes).
    Removing lfs.o(i.lfs_dir_compact), (1240 bytes).
    Removing lfs.o(i.lfs_dir_drop), (100 bytes).
    Removing lfs.o(i.lfs_dir_fetch), (34 bytes).
    Removing lfs.o(i.lfs_dir_fetchmatch), (1220 bytes).
    Removing lfs.o(i.lfs_dir_find), (412 bytes).
    Removing lfs.o(i.lfs_dir_find_match), (114 bytes).
    Removing lfs.o(i.lfs_dir_get), (46 bytes).
    Removing lfs.o(i.lfs_dir_getgstate), (96 bytes).
    Removing lfs.o(i.lfs_dir_getinfo), (184 bytes).
    Removing lfs.o(i.lfs_dir_getread), (302 bytes).
    Removing lfs.o(i.lfs_dir_getslice), (336 bytes).
    Removing lfs.o(i.lfs_dir_open), (188 bytes).
    Removing lfs.o(i.lfs_dir_read), (184 bytes).
    Removing lfs.o(i.lfs_dir_rewind), (46 bytes).
    Removing lfs.o(i.lfs_dir_seek), (114 bytes).
    Removing lfs.o(i.lfs_dir_split), (122 bytes).
    Removing lfs.o(i.lfs_dir_tell), (6 bytes).
    Removing lfs.o(i.lfs_dir_traverse), (568 bytes).
    Removing lfs.o(i.lfs_dir_traverse_filter), (140 bytes).
    Removing lfs.o(i.lfs_file_close), (148 bytes).
    Removing lfs.o(i.lfs_file_flush), (408 bytes).
    Removing lfs.o(i.lfs_file_open), (40 bytes).
    Removing lfs.o(i.lfs_file_opencfg), (692 bytes).
    Removing lfs.o(i.lfs_file_outline), (44 bytes).
    Removing lfs.o(i.lfs_file_read), (468 bytes).
    Removing lfs.o(i.lfs_file_relocate), (404 bytes).
    Removing lfs.o(i.lfs_file_rewind), (32 bytes).
    Removing lfs.o(i.lfs_file_seek), (160 bytes).
    Removing lfs.o(i.lfs_file_size), (108 bytes).
    Removing lfs.o(i.lfs_file_sync), (324 bytes).
    Removing lfs.o(i.lfs_file_tell), (88 bytes).
    Removing lfs.o(i.lfs_file_truncate), (360 bytes).
    Removing lfs.o(i.lfs_file_write), (692 bytes).
    Removing lfs.o(i.lfs_format), (220 bytes).
    Removing lfs.o(i.lfs_free), (12 bytes).
    Removing lfs.o(i.lfs_frombe32), (32 bytes).
    Removing lfs.o(i.lfs_fromle32), (32 bytes).
    Removing lfs.o(i.lfs_fs_demove), (128 bytes).
    Removing lfs.o(i.lfs_fs_deorphan), (416 bytes).
    Removing lfs.o(i.lfs_fs_forceconsistency), (36 bytes).
    Removing lfs.o(i.lfs_fs_parent), (108 bytes).
    Removing lfs.o(i.lfs_fs_parent_match), (88 bytes).
    Removing lfs.o(i.lfs_fs_pred), (78 bytes).
    Removing lfs.o(i.lfs_fs_prepmove), (40 bytes).
    Removing lfs.o(i.lfs_fs_preporphans), (60 bytes).
    Removing lfs.o(i.lfs_fs_relocate), (308 bytes).
    Removing lfs.o(i.lfs_fs_size), (36 bytes).
    Removing lfs.o(i.lfs_fs_size_count), (16 bytes).
    Removing lfs.o(i.lfs_fs_traverse), (344 bytes).
    Removing lfs.o(i.lfs_getattr), (160 bytes).
    Removing lfs.o(i.lfs_gstate_fromle32), (30 bytes).
    Removing lfs.o(i.lfs_gstate_hasmove), (20 bytes).
    Removing lfs.o(i.lfs_gstate_hasmovehere), (32 bytes).
    Removing lfs.o(i.lfs_gstate_hasorphans), (20 bytes).
    Removing lfs.o(i.lfs_gstate_iszero), (26 bytes).
    Removing lfs.o(i.lfs_gstate_tole32), (30 bytes).
    Removing lfs.o(i.lfs_gstate_xormove), (92 bytes).
    Removing lfs.o(i.lfs_gstate_xororphans), (20 bytes).
    Removing lfs.o(i.lfs_init), (1088 bytes).
    Removing lfs.o(i.lfs_malloc), (12 bytes).
    Removing lfs.o(i.lfs_max), (14 bytes).
    Removing lfs.o(i.lfs_min), (14 bytes).
    Removing lfs.o(i.lfs_mkdir), (396 bytes).
    Removing lfs.o(i.lfs_mount), (700 bytes).
    Removing lfs.o(i.lfs_npw2), (14 bytes).
    Removing lfs.o(i.lfs_pair_cmp), (42 bytes).
    Removing lfs.o(i.lfs_pair_fromle32), (22 bytes).
    Removing lfs.o(i.lfs_pair_isnull), (22 bytes).
    Removing lfs.o(i.lfs_pair_swap), (10 bytes).
    Removing lfs.o(i.lfs_pair_tole32), (22 bytes).
    Removing lfs.o(i.lfs_popc), (12 bytes).
    Removing lfs.o(i.lfs_remove), (288 bytes).
    Removing lfs.o(i.lfs_removeattr), (32 bytes).
    Removing lfs.o(i.lfs_rename), (568 bytes).
    Removing lfs.o(i.lfs_setattr), (48 bytes).
    Removing lfs.o(i.lfs_stat), (62 bytes).
    Removing lfs.o(i.lfs_superblock_fromle32), (54 bytes).
    Removing lfs.o(i.lfs_superblock_tole32), (54 bytes).
    Removing lfs.o(i.lfs_tag_chunk), (8 bytes).
    Removing lfs.o(i.lfs_tag_dsize), (22 bytes).
    Removing lfs.o(i.lfs_tag_id), (8 bytes).
    Removing lfs.o(i.lfs_tag_isdelete), (18 bytes).
    Removing lfs.o(i.lfs_tag_isvalid), (18 bytes).
    Removing lfs.o(i.lfs_tag_size), (8 bytes).
    Removing lfs.o(i.lfs_tag_splice), (14 bytes).
    Removing lfs.o(i.lfs_tag_type1), (10 bytes).
    Removing lfs.o(i.lfs_tag_type3), (8 bytes).
    Removing lfs.o(i.lfs_tobe32), (12 bytes).
    Removing lfs.o(i.lfs_tole32), (12 bytes).
    Removing lfs.o(i.lfs_unmount), (16 bytes).
    Removing lfs.o(.constdata), (448 bytes).
    Removing lfs.o(.conststring), (188 bytes).
    Removing lfs_port.o(.rev16_text), (4 bytes).
    Removing lfs_port.o(.revsh_text), (4 bytes).
    Removing lfs_port.o(.rrx_text), (6 bytes).
    Removing lfs_port.o(i.lfs_deskio_erase), (16 bytes).
    Removing lfs_port.o(i.lfs_deskio_prog), (32 bytes).
    Removing lfs_port.o(i.lfs_deskio_read), (32 bytes).
    Removing lfs_port.o(i.lfs_deskio_sync), (6 bytes).
    Removing lfs_port.o(i.lfs_storage_init), (112 bytes).
    Removing lfs_port.o(.bss), (544 bytes).
    Removing lfs_util.o(i.lfs_crc), (60 bytes).
    Removing lfs_util.o(.constdata), (64 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Allfill), (56 bytes).
    Removing oled.o(i.OLED_Display_Off), (22 bytes).
    Removing oled.o(i.OLED_Display_On), (22 bytes).
    Removing oled.o(i.OLED_Pow), (22 bytes).
    Removing oled.o(i.OLED_ShowFloat), (352 bytes).
    Removing oled.o(i.OLED_ShowHanzi), (100 bytes).
    Removing oled.o(i.OLED_ShowHzbig), (184 bytes).
    Removing oled.o(i.OLED_ShowNum), (136 bytes).
    Removing oled.o(i.OLED_ShowPic), (76 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_add_btn), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_add_btn_by_idx), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_register), (60 bytes).
    Removing ebtn.o(i.ebtn_get_btn_by_key_id), (80 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn), (12 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn_dyn), (12 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_key_id), (72 bytes).
    Removing ebtn.o(i.ebtn_get_config), (12 bytes).
    Removing ebtn.o(i.ebtn_get_total_btn_cnt), (28 bytes).
    Removing ebtn.o(i.ebtn_is_btn_active), (20 bytes).
    Removing ebtn.o(i.ebtn_is_btn_in_process), (20 bytes).
    Removing ebtn.o(i.ebtn_is_in_process), (132 bytes).
    Removing ebtn.o(i.ebtn_register), (72 bytes).
    Removing ebtn.o(i.ebtn_set_combo_suppress_threshold), (12 bytes).
    Removing ebtn.o(i.ebtn_set_config), (12 bytes).
    Removing sdio_sdcard.o(.rev16_text), (4 bytes).
    Removing sdio_sdcard.o(.revsh_text), (4 bytes).
    Removing sdio_sdcard.o(.rrx_text), (6 bytes).
    Removing sdio_sdcard.o(i.sd_erase), (324 bytes).
    Removing sdio_sdcard.o(i.sd_lock_unlock), (488 bytes).
    Removing sdio_sdcard.o(i.sd_power_off), (14 bytes).
    Removing sdio_sdcard.o(i.sd_sdstatus_get), (384 bytes).
    Removing sdio_sdcard.o(i.sd_transfer_state_get), (20 bytes).
    Removing ff.o(i.dir_read), (190 bytes).
    Removing ff.o(i.dir_remove), (96 bytes).
    Removing ff.o(i.f_chmod), (112 bytes).
    Removing ff.o(i.f_getfree), (276 bytes).
    Removing ff.o(i.f_lseek), (432 bytes).
    Removing ff.o(i.f_mount), (44 bytes).
    Removing ff.o(i.f_opendir), (136 bytes).
    Removing ff.o(i.f_read), (462 bytes).
    Removing ff.o(i.f_readdir), (112 bytes).
    Removing ff.o(i.f_rename), (320 bytes).
    Removing ff.o(i.f_stat), (82 bytes).
    Removing ff.o(i.f_truncate), (156 bytes).
    Removing ff.o(i.f_unlink), (202 bytes).
    Removing ff.o(i.f_utime), (110 bytes).
    Removing ff.o(i.get_fileinfo), (316 bytes).
    Removing ff.o(i.pick_lfn), (116 bytes).
    Removing diskio.o(.rev16_text), (4 bytes).
    Removing diskio.o(.revsh_text), (4 bytes).
    Removing diskio.o(.rrx_text), (6 bytes).
    Removing btn_app.o(.rev16_text), (4 bytes).
    Removing btn_app.o(.revsh_text), (4 bytes).
    Removing btn_app.o(.rrx_text), (6 bytes).
    Removing led_app.o(.rev16_text), (4 bytes).
    Removing led_app.o(.revsh_text), (4 bytes).
    Removing led_app.o(.rrx_text), (6 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(.rrx_text), (6 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing usart_app.o(.rev16_text), (4 bytes).
    Removing usart_app.o(.revsh_text), (4 bytes).
    Removing usart_app.o(.rrx_text), (6 bytes).
    Removing sd_app.o(.rev16_text), (4 bytes).
    Removing sd_app.o(.revsh_text), (4 bytes).
    Removing sd_app.o(.rrx_text), (6 bytes).
    Removing sd_app.o(i.card_info_get), (968 bytes).
    Removing sd_app.o(i.memory_compare), (36 bytes).
    Removing sd_app.o(i.sd_fatfs_test), (556 bytes).
    Removing sd_app.o(.bss), (1436 bytes).
    Removing sd_app.o(.data), (16 bytes).
    Removing adc_app.o(.rev16_text), (4 bytes).
    Removing adc_app.o(.revsh_text), (4 bytes).
    Removing adc_app.o(.rrx_text), (6 bytes).
    Removing rtc_app.o(.rev16_text), (4 bytes).
    Removing rtc_app.o(.revsh_text), (4 bytes).
    Removing rtc_app.o(.rrx_text), (6 bytes).
    Removing selftest_app.o(.rev16_text), (4 bytes).
    Removing selftest_app.o(.revsh_text), (4 bytes).
    Removing selftest_app.o(.rrx_text), (6 bytes).
    Removing sampling_app.o(.rev16_text), (4 bytes).
    Removing sampling_app.o(.revsh_text), (4 bytes).
    Removing sampling_app.o(.rrx_text), (6 bytes).
    Removing config_app.o(.rev16_text), (4 bytes).
    Removing config_app.o(.revsh_text), (4 bytes).
    Removing config_app.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (96 bytes).
    Removing gd32f4xx_adc.o(i.adc_deinit), (20 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (34 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (124 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (46 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (58 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_resolution_config), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_data_read), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_routine_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (50 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_sequence_channel_enable), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (56 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (52 bytes).
    Removing gd32f4xx_can.o(i.can_error_get), (12 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (32 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (272 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (30 bytes).
    Removing gd32f4xx_can.o(i.can_init), (290 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_get), (116 bytes).
    Removing gd32f4xx_can.o(i.can_message_receive), (228 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (336 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_receive_message_length_get), (26 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (164 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (80 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (10 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (124 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (48 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (168 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (36 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (16 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (48 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (12 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (38 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (22 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (26 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (20 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (20 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (36 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (52 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_get), (516 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (40 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (126 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (66 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (28 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (108 bytes).
    Removing gd32f4xx_enet.o(i.enet_default_init), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_delay), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_chain_init), (200 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_enable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_receive), (248 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_transmit), (204 bytes).
    Removing gd32f4xx_enet.o(i.enet_init), (868 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (356 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_enable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_set), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_config), (216 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_write_read), (156 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (50 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (280 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (256 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (340 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (444 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (56 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt), (10 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_enable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_drop), (172 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_size_get), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_software_reset), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_transmit_checksum_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_txfifo_flush), (52 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (20 bytes).
    Removing gd32f4xx_enet.o(.bss), (15460 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_enet.o(.data), (20 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (72 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (172 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_init), (228 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (40 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (106 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (188 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (284 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_command_para_init), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (66 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (64 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (28 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (188 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (80 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_halfword_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (72 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_page_erase), (124 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_ready_wait), (32 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_sector_erase), (96 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_state_get), (76 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_word_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_double_bank_select), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (96 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (84 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (76 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (40 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (52 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (72 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (72 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_prescaler_value_config), (60 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_reload_value_config), (64 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_toggle), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_write), (10 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (206 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (316 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (36 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (20 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (28 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f4xx_misc.o(i.nvic_vector_table_set), (36 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_misc.o(i.systick_clksource_set), (40 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_deepsleepmode), (244 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (108 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ck48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout0_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (140 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_on), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_stab_wait), (348 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (132 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (44 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (72 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_get), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (128 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (68 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (116 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_current_time_get), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (204 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_clear), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_init), (196 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_init_mode_enter), (72 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_init_mode_exit), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_register_sync_wait), (96 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (200 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (92 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (76 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (20 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (48 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (292 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_set), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_receive), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_transmit), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_deinit), (172 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_flag_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_format_error_clear), (6 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_read_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_write_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (28 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (172 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (24 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_config), (492 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_mode_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config), (38 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_shadow_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (188 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (152 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (48 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (228 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_trng.o(i.trng_deinit), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_flag_get), (24 bytes).
    Removing gd32f4xx_trng.o(i.trng_get_true_random_data), (12 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (28 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_clear), (52 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_cts_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_rts_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_disable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_flag_clear), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (104 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_stop_bit_set), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (34 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_word_length_set), (16 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (28 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (16 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (12 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(.rrx_text), (6 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (272 bytes).
    Removing perf_counter.o(.rev16_text), (4 bytes).
    Removing perf_counter.o(.revsh_text), (4 bytes).
    Removing perf_counter.o(.rrx_text), (6 bytes).
    Removing perf_counter.o(i.EventRecorderTimerGetCount), (8 bytes).
    Removing perf_counter.o(i.EventRecorderTimerGetFreq), (8 bytes).
    Removing perf_counter.o(i.EventRecorderTimerSetup), (4 bytes).
    Removing perf_counter.o(i.__on_context_switch_in), (76 bytes).
    Removing perf_counter.o(i.__on_context_switch_out), (112 bytes).
    Removing perf_counter.o(i.__perfc_is_time_out), (100 bytes).
    Removing perf_counter.o(i.__start_task_cycle_counter), (80 bytes).
    Removing perf_counter.o(i.__stop_task_cycle_counter), (148 bytes).
    Removing perf_counter.o(i.before_cycle_counter_reconfiguration), (64 bytes).
    Removing perf_counter.o(i.clock), (8 bytes).
    Removing perf_counter.o(i.delay_us), (84 bytes).
    Removing perf_counter.o(i.disable_task_cycle_info), (54 bytes).
    Removing perf_counter.o(i.enable_task_cycle_info), (58 bytes).
    Removing perf_counter.o(i.get_rtos_task_cycle_info), (4 bytes).
    Removing perf_counter.o(i.get_system_us), (128 bytes).
    Removing perf_counter.o(i.init_task_cycle_counter), (44 bytes).
    Removing perf_counter.o(i.init_task_cycle_info), (38 bytes).
    Removing perf_counter.o(i.perfc_check_task_stack_canary_safe), (52 bytes).
    Removing perf_counter.o(i.perfc_convert_ms_to_ticks), (44 bytes).
    Removing perf_counter.o(i.perfc_convert_ticks_to_ms), (28 bytes).
    Removing perf_counter.o(i.perfc_convert_ticks_to_us), (28 bytes).
    Removing perf_counter.o(i.perfc_convert_us_to_ticks), (44 bytes).
    Removing perf_counter.o(i.perfc_get_systimer_frequency), (8 bytes).
    Removing perf_counter.o(i.register_task_cycle_agent), (92 bytes).
    Removing perf_counter.o(i.resume_task_cycle_info), (16 bytes).
    Removing perf_counter.o(i.unregister_task_cycle_agent), (74 bytes).
    Removing perfc_port_default.o(.rev16_text), (4 bytes).
    Removing perfc_port_default.o(.revsh_text), (4 bytes).
    Removing perfc_port_default.o(.rrx_text), (6 bytes).
    Removing perfc_port_default.o(i.perfc_port_clear_system_timer_counter), (10 bytes).
    Removing perfc_port_default.o(i.perfc_port_stop_system_timer_counting), (18 bytes).
    Removing lfs.o(i.__ARM_pop), (48 bytes).

1127 unused section(s) (total 84872 bytes) removed from the image.
