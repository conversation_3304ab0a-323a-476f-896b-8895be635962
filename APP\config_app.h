/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/14
* Note: 配置文件管理应用层头文件
*/

#ifndef CONFIG_APP_H
#define CONFIG_APP_H

#include "mcu_cmic_gd32f470vet6.h"

// 配置参数结构体
typedef struct {
    float ratio;    // 比例值 (0-100)
    int limit;      // 限制值 (0-500)
} config_params_t;

// 配置状态枚举
typedef enum {
    CONFIG_STATE_NORMAL = 0,
    CONFIG_STATE_WAIT_RATIO,
    CONFIG_STATE_WAIT_LIMIT
} config_state_t;

// 全局变量声明
extern config_params_t config_params;
extern config_state_t config_state;

// 函数声明
void config_app_init(void);                    // 配置应用初始化
int config_read_from_tf(void);                 // 从TF卡读取配置文件
int config_write_to_tf(void);                  // 写入配置文件到TF卡
void config_cmd_handler(void);                 // 配置命令处理
void config_ratio_handler(void);               // ratio命令处理
void config_limit_handler(void);               // limit命令处理
int config_set_ratio(float new_ratio);         // 设置ratio值
int config_set_limit(int new_limit);           // 设置limit值
void config_process_input(const char* input);  // 处理用户输入

#endif // CONFIG_APP_H
