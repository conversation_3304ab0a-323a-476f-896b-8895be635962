/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/05
* Note:
*/

#include "mcu_cmic_gd32f470vet6.h"
#include "sampling_app.h"

extern uint16_t adc_value[1];

/**
 * @brief	ʹ������printf�ķ�ʽ��ʾ�ַ�������ʾ6x8��С��ASCII�ַ�
 * @param x  Character position on the X-axis  range��0 - 127
 * @param y  Character position on the Y-axis  range��0 - 3
 * ���磺oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // ��ʱ�洢��ʽ������ַ���
  va_list arg;      // �����ɱ����
  int len;          // �����ַ�������

  va_start(arg, format);
  // ��ȫ�ظ�ʽ���ַ����� buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}

void oled_task(void)
{
    extern rtc_time_t current_time;

    if(sampling_state == SAMPLING_START) {
        // 采样状态：显示时间和电压
        char time_str[16];
        sprintf(time_str, "%02d:%02d:%02d", current_time.hour, current_time.minute, current_time.second);
        oled_printf(0, 0, "%s", time_str);

        // 显示电压值，保留两位小数
        float voltage = adc_to_voltage(adc_value[0]);
        oled_printf(0, 1, "%.2fV", voltage);
    } else {
        // 停止状态：只显示system idle
        oled_printf(0, 0, "system idle");
        oled_printf(0, 1, "        "); // 清除第二行
    }
}

/* CUSTOM EDIT */
