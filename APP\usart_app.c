/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/05
* Note:
*/
#include "mcu_cmic_gd32f470vet6.h"
#include "selftest_app.h"
#include "sampling_app.h"

__IO uint8_t tx_count = 0;
__IO uint8_t rx_flag = 0;
uint8_t uart_dma_buffer[256] = {0};

int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[256];
    va_list arg;
    int len;
    // ????????????��?
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);
    
    for(tx_count = 0; tx_count < len; tx_count++){
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE));
        usart_data_transmit(usart_periph, buffer[tx_count]);
    }
    
    return len;
}

// 解析时间字符串函数
static int parse_datetime(const char* datetime_str, uint8_t* year, uint8_t* month, uint8_t* day, uint8_t* hour, uint8_t* minute, uint8_t* second)
{
    int full_year, temp_month, temp_day, temp_hour, temp_minute, temp_second;

    // 解析格式: "2025-01-01 12:00:30"
    if(sscanf(datetime_str, "%d-%d-%d %d:%d:%d", &full_year, &temp_month, &temp_day, &temp_hour, &temp_minute, &temp_second) == 6) {
        *year = full_year % 100; // 取年份后两位
        *month = temp_month;
        *day = temp_day;
        *hour = temp_hour;
        *minute = temp_minute;
        *second = temp_second;

        // 简单验证
        if(*month >= 1 && *month <= 12 && *day >= 1 && *day <= 31 &&
           *hour <= 23 && *minute <= 59 && *second <= 59) {
            return 1; // 成功
        }
    }
    return 0; // 失败
}

void uart_task(void)
{
    if(!rx_flag) return;

    // 确保字符串以NULL结尾
    uart_dma_buffer[255] = '\0';

    // 移除字符串末尾的换行符和回车符
    char* cmd = (char*)uart_dma_buffer;
    int len = strlen(cmd);

    // 移除末尾的控制字符
    while(len > 0 && (cmd[len-1] == '\r' || cmd[len-1] == '\n' || cmd[len-1] == ' ' || cmd[len-1] == '\t')) {
        cmd[len-1] = '\0';
        len--;
    }

    // 移除开头的空格
    while(*cmd == ' ' || *cmd == '\t') {
        cmd++;
        len--;
    }

    // 如果命令为空，直接返回
    if(len == 0 || *cmd == '\0') {
        rx_flag = 0;
        memset(uart_dma_buffer, 0, 256);
        return;
    }

    // 命令处理 - 使用更健壮的字符串比较
    if(strncmp(cmd, "test", 4) == 0 && len == 4) {
        system_selftest();
    }
    else if(strncmp(cmd, "start", 5) == 0 && len == 5) {
        sampling_start();
    }
    else if(strncmp(cmd, "stop", 4) == 0 && len == 4) {
        sampling_stop();
    }
    else if(strncmp(cmd, "RTC Config", 10) == 0 && len == 10) {
        my_printf(DEBUG_USART, "Input Datetime\r\n");
    }
    else if(strncmp(cmd, "RTC now", 7) == 0 && len == 7) {
        char time_str[32];
        rtc_get_time_string(time_str, sizeof(time_str));
        my_printf(DEBUG_USART, "Current TIME:%s\r\n", time_str);
    }
    // 处理时间设置命令（格式：2025-01-01 12:00:30）
    else if(len == 19 && cmd[4] == '-' && cmd[7] == '-' && cmd[10] == ' ' && cmd[13] == ':' && cmd[16] == ':') {
        uint8_t year, month, day, hour, minute, second;
        if(parse_datetime(cmd, &year, &month, &day, &hour, &minute, &second)) {
            rtc_set_time(year, month, day, hour, minute, second);
            my_printf(DEBUG_USART, "RTC Config success\r\nTime:%s\r\n", cmd);
        } else {
            my_printf(DEBUG_USART, "Invalid datetime format\r\n");
        }
    }
    // 其他命令回显（用于调试）
    else {
        my_printf(DEBUG_USART, "Unknown command: [%s] (len=%d)\r\n", cmd, len);
        // 显示每个字符的ASCII码用于调试
        my_printf(DEBUG_USART, "ASCII: ");
        for(int i = 0; i < len && i < 20; i++) {
            my_printf(DEBUG_USART, "%02X ", (uint8_t)cmd[i]);
        }
        my_printf(DEBUG_USART, "\r\n");
    }

    rx_flag = 0; // 清除接收标志
    memset(uart_dma_buffer, 0, 256);
}


