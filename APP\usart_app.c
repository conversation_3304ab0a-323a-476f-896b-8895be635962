/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/05
* Note:
*/
#include "mcu_cmic_gd32f470vet6.h"

__IO uint8_t tx_count = 0;
__IO uint8_t rx_flag = 0;
uint8_t uart_dma_buffer[256] = {0};

int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[256];
    va_list arg;
    int len;
    // ????????????��?
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);
    
    for(tx_count = 0; tx_count < len; tx_count++){
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE));
        usart_data_transmit(usart_periph, buffer[tx_count]);
    }
    
    return len;
}

// 解析时间字符串函数
static int parse_datetime(const char* datetime_str, uint8_t* year, uint8_t* month, uint8_t* day, uint8_t* hour, uint8_t* minute, uint8_t* second)
{
    int full_year, temp_month, temp_day, temp_hour, temp_minute, temp_second;

    // 解析格式: "2025-01-01 12:00:30"
    if(sscanf(datetime_str, "%d-%d-%d %d:%d:%d", &full_year, &temp_month, &temp_day, &temp_hour, &temp_minute, &temp_second) == 6) {
        *year = full_year % 100; // 取年份后两位
        *month = temp_month;
        *day = temp_day;
        *hour = temp_hour;
        *minute = temp_minute;
        *second = temp_second;

        // 简单验证
        if(*month >= 1 && *month <= 12 && *day >= 1 && *day <= 31 &&
           *hour <= 23 && *minute <= 59 && *second <= 59) {
            return 1; // 成功
        }
    }
    return 0; // 失败
}

void uart_task(void)
{
    if(!rx_flag) return;

    // 移除字符串末尾的换行符和回车符
    char* cmd = (char*)uart_dma_buffer;
    int len = strlen(cmd);
    while(len > 0 && (cmd[len-1] == '\r' || cmd[len-1] == '\n' || cmd[len-1] == ' ')) {
        cmd[len-1] = '\0';
        len--;
    }

    // 处理RTC Config命令
    if(strcmp(cmd, "RTC Config") == 0) {
        my_printf(DEBUG_USART, "Input Datetime\r\n");
    }
    // 处理RTC now命令
    else if(strcmp(cmd, "RTC now") == 0) {
        char time_str[32];
        rtc_get_time_string(time_str, sizeof(time_str));
        my_printf(DEBUG_USART, "Current TIME:%s\r\n", time_str);
    }
    // 处理时间设置命令（格式：2025-01-01 12:00:30）
    else if(strlen(cmd) == 19 && cmd[4] == '-' && cmd[7] == '-' && cmd[10] == ' ' && cmd[13] == ':' && cmd[16] == ':') {
        uint8_t year, month, day, hour, minute, second;
        if(parse_datetime(cmd, &year, &month, &day, &hour, &minute, &second)) {
            rtc_set_time(year, month, day, hour, minute, second);
            my_printf(DEBUG_USART, "RTC Config success\r\nTime:%s\r\n", cmd);
        } else {
            my_printf(DEBUG_USART, "Invalid datetime format\r\n");
        }
    }
    // 其他命令回显
    else if(strlen(cmd) > 0) {
        my_printf(DEBUG_USART, "%s\r\n", cmd);
    }

    rx_flag = 0; // 清除接收标志
    memset(uart_dma_buffer, 0, 256);
}

void print_adc_file_info(void)
{
    extern char adc_filename[32];
    extern uint8_t adc_recording_flag;

    if (strlen(adc_filename) == 0) {
        my_printf(DEBUG_USART, "No ADC file recorded yet.\r\n");
        return;
    }

    // ������ڼ�¼������ʾ�û�
    if (adc_recording_flag) {
        my_printf(DEBUG_USART, "Warning: ADC recording is still active\r\n");
    }

    FIL file;
    FRESULT result = f_open(&file, adc_filename, FA_READ);
    if (result != FR_OK) {
        my_printf(DEBUG_USART, "Failed to open ADC file: %s (Error: %d)\r\n", adc_filename, result);
        return;
    }

    // ��ȡ�ļ���С
    DWORD file_size = f_size(&file);
    my_printf(DEBUG_USART, "ADC File: %s\r\n", adc_filename);
    my_printf(DEBUG_USART, "File Size: %lu bytes\r\n", file_size);
    my_printf(DEBUG_USART, "File Content:\r\n");

    // ��ȡ����ӡ�ļ�����
    char read_buffer[128];
    UINT bytes_read;

    while (1) {
        result = f_read(&file, read_buffer, sizeof(read_buffer) - 1, &bytes_read);
        if (result != FR_OK || bytes_read == 0) break;

        read_buffer[bytes_read] = '\0'; // ȷ���ַ�������
        my_printf(DEBUG_USART, "%s", read_buffer);
    }

    f_close(&file);
    my_printf(DEBUG_USART, "\r\n--- End of File ---\r\n");
}
