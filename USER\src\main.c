/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/05/15
* Note:
*/
#include "mcu_cmic_gd32f470vet6.h"

int main(void)
{
#ifdef __FIRMWARE_VERSION_DEFINE
    uint32_t fw_ver = 0;
#endif
    systick_config();
    init_cycle_counter(false);
    delay_ms(200); // Wait download if SWIO be set to GPIO

#ifdef __FIRMWARE_VERSION_DEFINE
    fw_ver = gd32f4xx_firmware_version_get();
    /* print firmware version */
    //printf("\r\nGD32F4xx series firmware version: V%d.%d.%d", (uint8_t)(fw_ver >> 24), (uint8_t)(fw_ver >> 16), (uint8_t)(fw_ver >> 8));
#endif /* __FIRMWARE_VERSION_DEFINE */

    bsp_led_init();
    bsp_btn_init();
    bsp_oled_init();
    bsp_gd25qxx_init();
    bsp_usart_init();
    bsp_adc_init();
    bsp_dac_init();

    // 系统初始化完成打印
    my_printf(DEBUG_USART, "====system init====\r\n");

    // 读取并打印设备ID
    uint8_t device_id[32];
    spi_flash_buffer_read(device_id, 0x1000, 32); // 从Flash地址0x1000读取设备ID
    device_id[31] = '\0'; // 确保字符串结束

    // 检查是否为空或未初始化，如果是则写入默认设备ID
    if(device_id[0] == 0xFF || device_id[0] == 0x00 || strlen((char*)device_id) == 0) {
        const char* default_id = "2025-CIMC-2025680547";
        spi_flash_sector_erase(0x1000); // 擦除扇区
        spi_flash_buffer_write((uint8_t*)default_id, 0x1000, strlen(default_id) + 1);
        strcpy((char*)device_id, default_id);
    }

    my_printf(DEBUG_USART, "Device_ID:%s\r\n", device_id);

    sd_fatfs_init();
    app_btn_init();
    OLED_Init();
    adc_recording_init(); // ��ʼ��ADC��¼����
    rtc_app_init(); // 初始化RTC应用

    test_spi_flash();

    sd_fatfs_test();
    
    scheduler_init();
    while(1) 
    {
        scheduler_run();
    }
}

#ifdef GD_ECLIPSE_GCC
/* retarget the C library printf function to the USART, in Eclipse GCC environment */
int __io_putchar(int ch)
{
    usart_data_transmit(EVAL_COM0, (uint8_t)ch);
    while(RESET == usart_flag_get(EVAL_COM0, USART_FLAG_TBE));
    return ch;
}
#else
/* retarget the C library printf function to the USART */
int fputc(int ch, FILE *f)
{
    usart_data_transmit(USART0, (uint8_t)ch);
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
    return ch;
}
#endif /* GD_ECLIPSE_GCC */
