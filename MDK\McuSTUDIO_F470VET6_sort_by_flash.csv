File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,25.514305%,9711,96,9160,551,0,96
oled.o,10.446388%,3976,22,1242,2712,22,0
ebtn.o,5.438638%,2070,60,2070,0,0,60
btod.o,5.086572%,1936,0,1936,0,0,0
mcu_cmic_gd32f470vet6.o,3.783400%,1440,288,1432,0,8,280
_printf_fp_dec.o,2.769239%,1054,0,1054,0,0,0
perf_counter.o,2.601088%,990,80,906,4,80,0
gd32f4xx_dma.o,2.396153%,912,0,912,0,0,0
_scanf.o,2.322587%,884,0,884,0,0,0
_printf_fp_hex.o,2.107144%,802,0,764,38,0,0
usart_app.o,1.886445%,718,258,716,0,2,256
system_gd32f4xx.o,1.833898%,698,4,694,0,4,0
sdio_sdcard.o,1.791860%,682,36,646,0,36,0
gd32f4xx_adc.o,1.718294%,654,0,654,0,0,0
gd32f4xx_usart.o,1.692021%,644,0,644,0,0,0
gd32f4xx_timer.o,1.544888%,588,0,588,0,0,0
btn_app.o,1.481832%,564,196,354,14,196,0
rtc_app.o,1.366228%,520,16,504,0,16,0
gd32f4xx_i2c.o,1.303171%,496,0,496,0,0,0
startup_gd32f450_470.o,1.292662%,492,2048,64,428,0,2048
__printf_flags_ss_wp.o,1.074591%,409,0,392,17,0,0
gd32f4xx_rcu.o,1.050945%,400,0,400,0,0,0
bigflt0.o,0.987888%,376,0,228,148,0,0
gd25qxx.o,0.956360%,364,0,364,0,0,0
_scanf_int.o,0.872284%,332,0,332,0,0,0
lc_ctype_c.o,0.830246%,316,0,44,272,0,0
gd32f4xx_gpio.o,0.688369%,262,0,262,0,0,0
led_app.o,0.659468%,251,7,244,0,7,0
lludivv7m.o,0.625312%,238,0,238,0,0,0
gd32f4xx_sdio.o,0.620057%,236,0,236,0,0,0
gd32f4xx_misc.o,0.567510%,216,0,216,0,0,0
main.o,0.525472%,200,0,200,0,0,0
gd32f4xx_dac.o,0.520218%,198,0,198,0,0,0
_printf_wctomb.o,0.514963%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.493944%,188,0,148,40,0,0
gd32f4xx_it.o,0.472925%,180,0,180,0,0,0
_printf_intcommon.o,0.467670%,178,0,178,0,0,0
scheduler.o,0.462416%,176,76,100,0,76,0
systick.o,0.441397%,168,4,164,0,4,0
perfc_port_default.o,0.404614%,154,0,154,0,0,0
rt_memcpy_v6.o,0.362576%,138,0,138,0,0,0
lludiv10.o,0.362576%,138,0,138,0,0,0
strcmpv7m.o,0.336302%,128,0,128,0,0,0
_printf_fp_infnan.o,0.336302%,128,0,128,0,0,0
_printf_longlong_dec.o,0.325793%,124,0,124,0,0,0
_printf_dec.o,0.315283%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.294264%,112,0,112,0,0,0
gd32f4xx_spi.o,0.273246%,104,0,104,0,0,0
rt_memcpy_w.o,0.262736%,100,0,100,0,0,0
__dczerorl2.o,0.236463%,90,0,90,0,0,0
memcmp.o,0.231208%,88,0,88,0,0,0
oled_app.o,0.231208%,88,0,88,0,0,0
_printf_str.o,0.215444%,82,0,82,0,0,0
rt_memclr_w.o,0.204934%,78,0,78,0,0,0
_printf_pad.o,0.204934%,78,0,78,0,0,0
sys_stackheap_outer.o,0.194425%,74,0,74,0,0,0
llsdiv.o,0.189170%,72,0,72,0,0,0
lc_numeric_c.o,0.189170%,72,0,44,28,0,0
rt_memclr.o,0.178661%,68,0,68,0,0,0
_wcrtomb.o,0.168151%,64,0,64,0,0,0
_sgetc.o,0.168151%,64,0,64,0,0,0
strlen.o,0.162896%,62,0,62,0,0,0
__0sscanf.o,0.157642%,60,0,60,0,0,0
__2snprintf.o,0.147132%,56,0,56,0,0,0
vsnprintf.o,0.136623%,52,0,52,0,0,0
__scatter.o,0.136623%,52,0,52,0,0,0
m_wm.l,0.126113%,48,0,48,0,0,0
fpclassify.o,0.126113%,48,0,48,0,0,0
_printf_char_common.o,0.126113%,48,0,48,0,0,0
scanf_char.o,0.115604%,44,0,44,0,0,0
_printf_wchar.o,0.115604%,44,0,44,0,0,0
_printf_char.o,0.115604%,44,0,44,0,0,0
_printf_charcount.o,0.105094%,40,0,40,0,0,0
libinit2.o,0.099840%,38,0,38,0,0,0
init_aeabi.o,0.094585%,36,0,36,0,0,0
_printf_truncate.o,0.094585%,36,0,36,0,0,0
systick_wrapper_ual.o,0.084076%,32,0,32,0,0,0
_chval.o,0.073566%,28,0,28,0,0,0
__scatter_zi.o,0.073566%,28,0,28,0,0,0
adc_app.o,0.052547%,20,0,20,0,0,0
fz_wm.l,0.047293%,18,0,18,0,0,0
isspace.o,0.047293%,18,0,18,0,0,0
exit.o,0.047293%,18,0,18,0,0,0
rt_ctype_table.o,0.042038%,16,0,16,0,0,0
_snputc.o,0.042038%,16,0,16,0,0,0
__printf_wp.o,0.036783%,14,0,14,0,0,0
sd_app.o,0.036783%,14,0,14,0,0,0
sys_exit.o,0.031528%,12,0,12,0,0,0
__rtentry2.o,0.031528%,12,0,12,0,0,0
fpinit.o,0.026274%,10,0,10,0,0,0
rtexit2.o,0.026274%,10,0,10,0,0,0
_sputc.o,0.026274%,10,0,10,0,0,0
_printf_ll.o,0.026274%,10,0,10,0,0,0
_printf_l.o,0.026274%,10,0,10,0,0,0
rt_locale_intlibspace.o,0.021019%,8,0,8,0,0,0
libspace.o,0.021019%,8,96,8,0,0,96
__main.o,0.021019%,8,0,8,0,0,0
heapauxi.o,0.015764%,6,0,6,0,0,0
_printf_x.o,0.015764%,6,0,6,0,0,0
_printf_u.o,0.015764%,6,0,6,0,0,0
_printf_s.o,0.015764%,6,0,6,0,0,0
_printf_p.o,0.015764%,6,0,6,0,0,0
_printf_o.o,0.015764%,6,0,6,0,0,0
_printf_n.o,0.015764%,6,0,6,0,0,0
_printf_ls.o,0.015764%,6,0,6,0,0,0
_printf_llx.o,0.015764%,6,0,6,0,0,0
_printf_llu.o,0.015764%,6,0,6,0,0,0
_printf_llo.o,0.015764%,6,0,6,0,0,0
_printf_lli.o,0.015764%,6,0,6,0,0,0
_printf_lld.o,0.015764%,6,0,6,0,0,0
_printf_lc.o,0.015764%,6,0,6,0,0,0
_printf_i.o,0.015764%,6,0,6,0,0,0
_printf_g.o,0.015764%,6,0,6,0,0,0
_printf_f.o,0.015764%,6,0,6,0,0,0
_printf_e.o,0.015764%,6,0,6,0,0,0
_printf_d.o,0.015764%,6,0,6,0,0,0
_printf_c.o,0.015764%,6,0,6,0,0,0
_printf_a.o,0.015764%,6,0,6,0,0,0
__rtentry4.o,0.015764%,6,0,6,0,0,0
printf2.o,0.010509%,4,0,4,0,0,0
printf1.o,0.010509%,4,0,4,0,0,0
_printf_percent_end.o,0.010509%,4,0,4,0,0,0
use_no_semi.o,0.005255%,2,0,2,0,0,0
rtexit.o,0.005255%,2,0,2,0,0,0
libshutdown2.o,0.005255%,2,0,2,0,0,0
libshutdown.o,0.005255%,2,0,2,0,0,0
libinit.o,0.005255%,2,0,2,0,0,0
