File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,19.149057%,13403,96,12852,551,0,96
sdio_sdcard.o,10.243881%,7170,68,7134,0,36,32
ff.o,9.670967%,6769,6,6750,13,6,0
oled.o,5.680568%,3976,22,1242,2712,22,0
config_app.o,3.156030%,2209,13,2196,0,13,0
btod.o,3.074593%,2152,0,2152,0,0,0
ebtn.o,2.957439%,2070,60,2070,0,0,60
fz_wm.l,2.174503%,1522,0,1506,16,0,0
mcu_cmic_gd32f470vet6.o,2.057348%,1440,288,1432,0,8,280
gd32f4xx_dma.o,1.903047%,1332,0,1332,0,0,0
scanf_fp.o,1.817325%,1272,0,1272,0,0,0
usart_app.o,1.591588%,1114,258,1112,0,2,256
_printf_fp_dec.o,1.505865%,1054,0,1054,0,0,0
perf_counter.o,1.414427%,990,80,906,4,80,0
gd25qxx.o,1.414427%,990,0,990,0,0,0
_scanf.o,1.262983%,884,0,884,0,0,0
m_wm.l,1.145829%,802,0,802,0,0,0
_printf_fp_hex.o,1.145829%,802,0,764,38,0,0
scanf_hexfp.o,1.142971%,800,0,800,0,0,0
selftest_app.o,1.125827%,788,0,572,216,0,0
fatfs_unicode.o,1.020102%,714,0,498,216,0,0
system_gd32f4xx.o,0.997243%,698,4,694,0,4,0
gd32f4xx_adc.o,0.934379%,654,0,654,0,0,0
sampling_app.o,0.928664%,650,14,636,0,14,0
gd32f4xx_usart.o,0.920092%,644,0,644,0,0,0
gd32f4xx_sdio.o,0.897233%,628,0,628,0,0,0
gd32f4xx_timer.o,0.840084%,588,0,588,0,0,0
btn_app.o,0.800080%,560,196,350,14,196,0
rtc_app.o,0.742931%,520,16,504,0,16,0
gd32f4xx_i2c.o,0.708642%,496,0,496,0,0,0
startup_gd32f450_470.o,0.702927%,492,2048,64,428,0,2048
diskio.o,0.588630%,412,0,412,0,0,0
__printf_flags_ss_wp.o,0.584344%,409,0,392,17,0,0
gd32f4xx_rcu.o,0.571486%,400,0,400,0,0,0
bigflt0.o,0.537197%,376,0,228,148,0,0
dmul.o,0.485763%,340,0,340,0,0,0
_scanf_int.o,0.474333%,332,0,332,0,0,0
lc_ctype_c.o,0.451474%,316,0,44,272,0,0
scanf_infnan.o,0.440044%,308,0,308,0,0,0
narrow.o,0.380038%,266,0,266,0,0,0
gd32f4xx_gpio.o,0.374323%,262,0,262,0,0,0
led_app.o,0.358607%,251,7,244,0,7,0
oled_app.o,0.354321%,248,0,248,0,0,0
lludivv7m.o,0.340034%,238,0,238,0,0,0
ldexp.o,0.325747%,228,0,228,0,0,0
gd32f4xx_misc.o,0.308602%,216,0,216,0,0,0
main.o,0.297173%,208,0,208,0,0,0
gd32f4xx_dac.o,0.282885%,198,0,198,0,0,0
_printf_wctomb.o,0.280028%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.268598%,188,0,148,40,0,0
scheduler.o,0.268598%,188,88,100,0,88,0
gd32f4xx_it.o,0.257169%,180,0,180,0,0,0
_printf_intcommon.o,0.254311%,178,0,178,0,0,0
systick.o,0.240024%,168,4,164,0,4,0
strtod.o,0.234309%,164,0,164,0,0,0
dnaninf.o,0.222879%,156,0,156,0,0,0
perfc_port_default.o,0.220022%,154,0,154,0,0,0
strncmp.o,0.214307%,150,0,150,0,0,0
frexp.o,0.200020%,140,0,140,0,0,0
fnaninf.o,0.200020%,140,0,140,0,0,0
rt_memcpy_v6.o,0.197163%,138,0,138,0,0,0
lludiv10.o,0.197163%,138,0,138,0,0,0
init_alloc.o,0.197163%,138,0,138,0,0,0
strcmpv7m.o,0.182875%,128,0,128,0,0,0
_printf_fp_infnan.o,0.182875%,128,0,128,0,0,0
_printf_longlong_dec.o,0.177161%,124,0,124,0,0,0
dleqf.o,0.171446%,120,0,120,0,0,0
deqf.o,0.171446%,120,0,120,0,0,0
_printf_dec.o,0.171446%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.160016%,112,0,112,0,0,0
drleqf.o,0.154301%,108,0,108,0,0,0
gd32f4xx_spi.o,0.148586%,104,0,104,0,0,0
retnan.o,0.142871%,100,0,100,0,0,0
rt_memcpy_w.o,0.142871%,100,0,100,0,0,0
d2f.o,0.140014%,98,0,98,0,0,0
h1_alloc.o,0.134299%,94,0,94,0,0,0
scalbn.o,0.131442%,92,0,92,0,0,0
__dczerorl2.o,0.128584%,90,0,90,0,0,0
memcmp.o,0.125727%,88,0,88,0,0,0
f2d.o,0.122869%,86,0,86,0,0,0
strncpy.o,0.122869%,86,0,86,0,0,0
_printf_str.o,0.117155%,82,0,82,0,0,0
defsig_rtmem_inner.o,0.114297%,80,0,80,0,0,0
rt_memclr_w.o,0.111440%,78,0,78,0,0,0
h1_free.o,0.111440%,78,0,78,0,0,0
_printf_pad.o,0.111440%,78,0,78,0,0,0
sys_stackheap_outer.o,0.105725%,74,0,74,0,0,0
llsdiv.o,0.102867%,72,0,72,0,0,0
lc_numeric_c.o,0.102867%,72,0,44,28,0,0
rt_memclr.o,0.097153%,68,0,68,0,0,0
dunder.o,0.091438%,64,0,64,0,0,0
_wcrtomb.o,0.091438%,64,0,64,0,0,0
_sgetc.o,0.091438%,64,0,64,0,0,0
strlen.o,0.088580%,62,0,62,0,0,0
__0sscanf.o,0.085723%,60,0,60,0,0,0
atof.o,0.080008%,56,0,56,0,0,0
__2snprintf.o,0.080008%,56,0,56,0,0,0
vsnprintf.o,0.074293%,52,0,52,0,0,0
h1_extend.o,0.074293%,52,0,52,0,0,0
__scatter.o,0.074293%,52,0,52,0,0,0
defsig_general.o,0.071436%,50,0,50,0,0,0
fpclassify.o,0.068578%,48,0,48,0,0,0
trapv.o,0.068578%,48,0,48,0,0,0
_printf_char_common.o,0.068578%,48,0,48,0,0,0
libinit2.o,0.065721%,46,0,46,0,0,0
scanf_char.o,0.062863%,44,0,44,0,0,0
_printf_wchar.o,0.062863%,44,0,44,0,0,0
_printf_char.o,0.062863%,44,0,44,0,0,0
__2sprintf.o,0.062863%,44,0,44,0,0,0
_printf_charcount.o,0.057149%,40,0,40,0,0,0
llshl.o,0.054291%,38,0,38,0,0,0
init_aeabi.o,0.051434%,36,0,36,0,0,0
_printf_truncate.o,0.051434%,36,0,36,0,0,0
systick_wrapper_ual.o,0.045719%,32,0,32,0,0,0
_chval.o,0.040004%,28,0,28,0,0,0
__scatter_zi.o,0.040004%,28,0,28,0,0,0
dcmpi.o,0.034289%,24,0,24,0,0,0
_rserrno.o,0.031432%,22,0,22,0,0,0
strchr.o,0.028574%,20,0,20,0,0,0
adc_app.o,0.028574%,20,0,20,0,0,0
isspace.o,0.025717%,18,0,18,0,0,0
exit.o,0.025717%,18,0,18,0,0,0
fpconst.o,0.022859%,16,0,0,16,0,0
dcheck1.o,0.022859%,16,0,16,0,0,0
rt_ctype_table.o,0.022859%,16,0,16,0,0,0
_snputc.o,0.022859%,16,0,16,0,0,0
sys_wrch.o,0.020002%,14,0,14,0,0,0
h1_init.o,0.020002%,14,0,14,0,0,0
defsig_rtmem_outer.o,0.020002%,14,0,14,0,0,0
__printf_wp.o,0.020002%,14,0,14,0,0,0
sd_app.o,0.020002%,14,0,14,0,0,0
dretinf.o,0.017145%,12,0,12,0,0,0
sys_exit.o,0.017145%,12,0,12,0,0,0
__rtentry2.o,0.017145%,12,0,12,0,0,0
fretinf.o,0.014287%,10,0,10,0,0,0
fpinit.o,0.014287%,10,0,10,0,0,0
rtexit2.o,0.014287%,10,0,10,0,0,0
defsig_exit.o,0.014287%,10,0,10,0,0,0
_sputc.o,0.014287%,10,0,10,0,0,0
_printf_ll.o,0.014287%,10,0,10,0,0,0
_printf_l.o,0.014287%,10,0,10,0,0,0
scanf2.o,0.011430%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.011430%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.011430%,8,0,8,0,0,0
libspace.o,0.011430%,8,96,8,0,0,96
__main.o,0.011430%,8,0,8,0,0,0
istatus.o,0.008572%,6,0,6,0,0,0
heapauxi.o,0.008572%,6,0,6,0,0,0
_printf_x.o,0.008572%,6,0,6,0,0,0
_printf_u.o,0.008572%,6,0,6,0,0,0
_printf_s.o,0.008572%,6,0,6,0,0,0
_printf_p.o,0.008572%,6,0,6,0,0,0
_printf_o.o,0.008572%,6,0,6,0,0,0
_printf_n.o,0.008572%,6,0,6,0,0,0
_printf_ls.o,0.008572%,6,0,6,0,0,0
_printf_llx.o,0.008572%,6,0,6,0,0,0
_printf_llu.o,0.008572%,6,0,6,0,0,0
_printf_llo.o,0.008572%,6,0,6,0,0,0
_printf_lli.o,0.008572%,6,0,6,0,0,0
_printf_lld.o,0.008572%,6,0,6,0,0,0
_printf_lc.o,0.008572%,6,0,6,0,0,0
_printf_i.o,0.008572%,6,0,6,0,0,0
_printf_g.o,0.008572%,6,0,6,0,0,0
_printf_f.o,0.008572%,6,0,6,0,0,0
_printf_e.o,0.008572%,6,0,6,0,0,0
_printf_d.o,0.008572%,6,0,6,0,0,0
_printf_c.o,0.008572%,6,0,6,0,0,0
_printf_a.o,0.008572%,6,0,6,0,0,0
__rtentry4.o,0.008572%,6,0,6,0,0,0
scanf1.o,0.005715%,4,0,4,0,0,0
printf2.o,0.005715%,4,0,4,0,0,0
printf1.o,0.005715%,4,0,4,0,0,0
hguard.o,0.005715%,4,0,4,0,0,0
_printf_percent_end.o,0.005715%,4,0,4,0,0,0
use_no_semi.o,0.002857%,2,0,2,0,0,0
rtexit.o,0.002857%,2,0,2,0,0,0
libshutdown2.o,0.002857%,2,0,2,0,0,0
libshutdown.o,0.002857%,2,0,2,0,0,0
libinit.o,0.002857%,2,0,2,0,0,0
