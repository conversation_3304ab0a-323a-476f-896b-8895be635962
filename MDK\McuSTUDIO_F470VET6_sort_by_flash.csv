File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,17.128609%,10407,96,9856,551,0,96
ff.o,11.977024%,7277,6,7258,13,6,0
sdio_sdcard.o,11.800915%,7170,68,7134,0,36,32
oled.o,6.543994%,3976,22,1242,2712,22,0
ebtn.o,3.406959%,2070,60,2070,0,0,60
btod.o,3.186412%,1936,0,1936,0,0,0
gd25qxx.o,2.900030%,1762,0,1762,0,0,0
sd_app.o,2.616939%,1590,1452,1574,0,16,1436
adc_app.o,2.449060%,1488,588,1480,0,8,580
mcu_cmic_gd32f470vet6.o,2.370058%,1440,288,1432,0,8,280
gd32f4xx_dma.o,2.192304%,1332,0,1332,0,0,0
usart_app.o,1.741334%,1058,258,1056,0,2,256
_printf_fp_dec.o,1.734751%,1054,0,1054,0,0,0
perf_counter.o,1.629415%,990,80,906,4,80,0
_scanf.o,1.454952%,884,0,884,0,0,0
_printf_fp_hex.o,1.319991%,802,0,764,38,0,0
fatfs_unicode.o,1.175154%,714,0,498,216,0,0
system_gd32f4xx.o,1.148820%,698,4,694,0,4,0
gd32f4xx_adc.o,1.076401%,654,0,654,0,0,0
gd32f4xx_usart.o,1.059943%,644,0,644,0,0,0
gd32f4xx_sdio.o,1.033609%,628,0,628,0,0,0
btn_app.o,0.974357%,592,196,382,14,196,0
rtc_app.o,0.967774%,588,16,572,0,16,0
gd32f4xx_timer.o,0.967774%,588,0,588,0,0,0
gd32f4xx_i2c.o,0.816353%,496,0,496,0,0,0
startup_gd32f450_470.o,0.809770%,492,2048,64,428,0,2048
diskio.o,0.678100%,412,0,412,0,0,0
__printf_flags_ss_wp.o,0.673162%,409,0,392,17,0,0
gd32f4xx_rcu.o,0.658350%,400,0,400,0,0,0
bigflt0.o,0.618849%,376,0,228,148,0,0
_scanf_int.o,0.546430%,332,0,332,0,0,0
lc_ctype_c.o,0.520096%,316,0,44,272,0,0
oled_app.o,0.487179%,296,0,296,0,0,0
gd32f4xx_gpio.o,0.431219%,262,0,262,0,0,0
main.o,0.427927%,260,0,260,0,0,0
fz_wm.l,0.421344%,256,0,256,0,0,0
led_app.o,0.413114%,251,7,244,0,7,0
lludivv7m.o,0.391718%,238,0,238,0,0,0
gd32f4xx_misc.o,0.355509%,216,0,216,0,0,0
gd32f4xx_dac.o,0.325883%,198,0,198,0,0,0
_printf_wctomb.o,0.322591%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.309424%,188,0,148,40,0,0
_printf_intcommon.o,0.292966%,178,0,178,0,0,0
scheduler.o,0.289674%,176,76,100,0,76,0
systick.o,0.276507%,168,4,164,0,4,0
gd32f4xx_it.o,0.276507%,168,0,168,0,0,0
perfc_port_default.o,0.253465%,154,0,154,0,0,0
fnaninf.o,0.230422%,140,0,140,0,0,0
rt_memcpy_v6.o,0.227131%,138,0,138,0,0,0
lludiv10.o,0.227131%,138,0,138,0,0,0
init_alloc.o,0.227131%,138,0,138,0,0,0
strcmpv7m.o,0.210672%,128,0,128,0,0,0
_printf_fp_infnan.o,0.210672%,128,0,128,0,0,0
_printf_longlong_dec.o,0.204088%,124,0,124,0,0,0
_printf_dec.o,0.197505%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.184338%,112,0,112,0,0,0
gd32f4xx_spi.o,0.171171%,104,0,104,0,0,0
rt_memcpy_w.o,0.164587%,100,0,100,0,0,0
h1_alloc.o,0.154712%,94,0,94,0,0,0
__dczerorl2.o,0.148129%,90,0,90,0,0,0
memcmp.o,0.144837%,88,0,88,0,0,0
f2d.o,0.141545%,86,0,86,0,0,0
_printf_str.o,0.134962%,82,0,82,0,0,0
defsig_rtmem_inner.o,0.131670%,80,0,80,0,0,0
rt_memclr_w.o,0.128378%,78,0,78,0,0,0
h1_free.o,0.128378%,78,0,78,0,0,0
_printf_pad.o,0.128378%,78,0,78,0,0,0
sys_stackheap_outer.o,0.121795%,74,0,74,0,0,0
strcpy.o,0.118503%,72,0,72,0,0,0
llsdiv.o,0.118503%,72,0,72,0,0,0
lc_numeric_c.o,0.118503%,72,0,44,28,0,0
rt_memclr.o,0.111919%,68,0,68,0,0,0
_wcrtomb.o,0.105336%,64,0,64,0,0,0
_sgetc.o,0.105336%,64,0,64,0,0,0
strlen.o,0.102044%,62,0,62,0,0,0
__0sscanf.o,0.098752%,60,0,60,0,0,0
__2snprintf.o,0.092169%,56,0,56,0,0,0
vsnprintf.o,0.085585%,52,0,52,0,0,0
h1_extend.o,0.085585%,52,0,52,0,0,0
__scatter.o,0.085585%,52,0,52,0,0,0
defsig_general.o,0.082294%,50,0,50,0,0,0
m_wm.l,0.079002%,48,0,48,0,0,0
fpclassify.o,0.079002%,48,0,48,0,0,0
_printf_char_common.o,0.079002%,48,0,48,0,0,0
libinit2.o,0.075710%,46,0,46,0,0,0
scanf_char.o,0.072418%,44,0,44,0,0,0
_printf_wchar.o,0.072418%,44,0,44,0,0,0
_printf_char.o,0.072418%,44,0,44,0,0,0
__2sprintf.o,0.072418%,44,0,44,0,0,0
_printf_charcount.o,0.065835%,40,0,40,0,0,0
init_aeabi.o,0.059251%,36,0,36,0,0,0
_printf_truncate.o,0.059251%,36,0,36,0,0,0
systick_wrapper_ual.o,0.052668%,32,0,32,0,0,0
_chval.o,0.046084%,28,0,28,0,0,0
__scatter_zi.o,0.046084%,28,0,28,0,0,0
isspace.o,0.029626%,18,0,18,0,0,0
exit.o,0.029626%,18,0,18,0,0,0
rt_ctype_table.o,0.026334%,16,0,16,0,0,0
aeabi_memset.o,0.026334%,16,0,16,0,0,0
_snputc.o,0.026334%,16,0,16,0,0,0
sys_wrch.o,0.023042%,14,0,14,0,0,0
h1_init.o,0.023042%,14,0,14,0,0,0
defsig_rtmem_outer.o,0.023042%,14,0,14,0,0,0
__printf_wp.o,0.023042%,14,0,14,0,0,0
dretinf.o,0.019750%,12,0,12,0,0,0
sys_exit.o,0.019750%,12,0,12,0,0,0
__rtentry2.o,0.019750%,12,0,12,0,0,0
fpinit.o,0.016459%,10,0,10,0,0,0
rtexit2.o,0.016459%,10,0,10,0,0,0
defsig_exit.o,0.016459%,10,0,10,0,0,0
_sputc.o,0.016459%,10,0,10,0,0,0
_printf_ll.o,0.016459%,10,0,10,0,0,0
_printf_l.o,0.016459%,10,0,10,0,0,0
rt_locale_intlibspace.o,0.013167%,8,0,8,0,0,0
libspace.o,0.013167%,8,96,8,0,0,96
__main.o,0.013167%,8,0,8,0,0,0
heapauxi.o,0.009875%,6,0,6,0,0,0
_printf_x.o,0.009875%,6,0,6,0,0,0
_printf_u.o,0.009875%,6,0,6,0,0,0
_printf_s.o,0.009875%,6,0,6,0,0,0
_printf_p.o,0.009875%,6,0,6,0,0,0
_printf_o.o,0.009875%,6,0,6,0,0,0
_printf_n.o,0.009875%,6,0,6,0,0,0
_printf_ls.o,0.009875%,6,0,6,0,0,0
_printf_llx.o,0.009875%,6,0,6,0,0,0
_printf_llu.o,0.009875%,6,0,6,0,0,0
_printf_llo.o,0.009875%,6,0,6,0,0,0
_printf_lli.o,0.009875%,6,0,6,0,0,0
_printf_lld.o,0.009875%,6,0,6,0,0,0
_printf_lc.o,0.009875%,6,0,6,0,0,0
_printf_i.o,0.009875%,6,0,6,0,0,0
_printf_g.o,0.009875%,6,0,6,0,0,0
_printf_f.o,0.009875%,6,0,6,0,0,0
_printf_e.o,0.009875%,6,0,6,0,0,0
_printf_d.o,0.009875%,6,0,6,0,0,0
_printf_c.o,0.009875%,6,0,6,0,0,0
_printf_a.o,0.009875%,6,0,6,0,0,0
__rtentry4.o,0.009875%,6,0,6,0,0,0
printf2.o,0.006583%,4,0,4,0,0,0
printf1.o,0.006583%,4,0,4,0,0,0
hguard.o,0.006583%,4,0,4,0,0,0
_printf_percent_end.o,0.006583%,4,0,4,0,0,0
use_no_semi.o,0.003292%,2,0,2,0,0,0
rtexit.o,0.003292%,2,0,2,0,0,0
libshutdown2.o,0.003292%,2,0,2,0,0,0
libshutdown.o,0.003292%,2,0,2,0,0,0
libinit.o,0.003292%,2,0,2,0,0,0
