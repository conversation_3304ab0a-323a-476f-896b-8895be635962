/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/14
* Note: 周期采样应用层实现
*/

#include "sampling_app.h"

// 全局变量定义
sampling_state_t sampling_state = SAMPLING_STOP;
uint32_t led_blink_timer = 0;
uint32_t sampling_timer = 0;
uint8_t led_blink_state = 0;
uint8_t sampling_count = 0;

extern uint16_t adc_value[1];

void sampling_app_init(void)
{
    sampling_state = SAMPLING_STOP;
    led_blink_timer = 0;
    sampling_timer = 0;
    led_blink_state = 0;
    sampling_count = 0;
    LED1_OFF; // 确保LED1初始状态为关闭
}

float adc_to_voltage(uint16_t adc_val)
{
    // ADC参考电压3.3V，12位ADC (0-4095)
    return (float)adc_val * 3.3f / 4095.0f;
}

void sampling_start(void)
{
    if(sampling_state == SAMPLING_STOP) {
        sampling_state = SAMPLING_START;
        led_blink_timer = get_system_ms();
        sampling_timer = get_system_ms();
        sampling_count = 0;
        
        my_printf(DEBUG_USART, "Period Sampling\r\n");
        my_printf(DEBUG_USART, "sample cycle :5s\r\n");
        
        // 立即输出第一次采样
        char time_str[32];
        rtc_get_time_string(time_str, sizeof(time_str));
        float voltage = adc_to_voltage(adc_value[0]);
        my_printf(DEBUG_USART, "%s ch0 = %.2fV\r\n", time_str, voltage);
        
        sampling_count = 1;
    }
}

void sampling_stop(void)
{
    if(sampling_state == SAMPLING_START) {
        sampling_state = SAMPLING_STOP;
        LED1_OFF; // 关闭LED1
        led_blink_state = 0;
        
        my_printf(DEBUG_USART, "Periodic Sampling  STOP\r\n");
    }
}

void sampling_toggle(void)
{
    if(sampling_state == SAMPLING_START) {
        sampling_stop();
    } else {
        sampling_start();
    }
}

void sampling_task(void)
{
    uint32_t current_time = get_system_ms();
    
    if(sampling_state == SAMPLING_START) {
        // LED1闪烁控制 (1秒周期)
        if(current_time - led_blink_timer >= 500) { // 500ms切换一次，形成1秒周期
            led_blink_timer = current_time;
            led_blink_state = !led_blink_state;
            LED1_SET(led_blink_state);
        }
        
        // 5秒周期采样
        if(current_time - sampling_timer >= 5000) {
            sampling_timer = current_time;
            
            char time_str[32];
            rtc_get_time_string(time_str, sizeof(time_str));
            float voltage = adc_to_voltage(adc_value[0]);
            my_printf(DEBUG_USART, "%s ch0 = %.2fV\r\n", time_str, voltage);
            
            sampling_count++;
        }
    }
}
