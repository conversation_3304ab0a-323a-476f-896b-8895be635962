.\output\sampling_app.o: ..\APP\sampling_app.c
.\output\sampling_app.o: .\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h
.\output\sampling_app.o: ..\APP\sampling_app.h
.\output\sampling_app.o: ..\Components\bsp\mcu_cmic_gd32f470vet6.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\PACK\perf_counter-CMSIS-Pack\CI\perf_counter_template_gcc\Drivers\CMSIS\Include\core_cm4.h
.\output\sampling_app.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\output\sampling_app.o: ..\PACK\perf_counter-CMSIS-Pack\CI\perf_counter_template_gcc\Drivers\CMSIS\Include\cmsis_version.h
.\output\sampling_app.o: ..\PACK\perf_counter-CMSIS-Pack\CI\perf_counter_template_gcc\Drivers\CMSIS\Include\cmsis_compiler.h
.\output\sampling_app.o: ..\PACK\perf_counter-CMSIS-Pack\CI\perf_counter_template_gcc\Drivers\CMSIS\Include\cmsis_armcc.h
.\output\sampling_app.o: ..\PACK\perf_counter-CMSIS-Pack\CI\perf_counter_template_gcc\Drivers\CMSIS\Include\mpu_armv7.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\output\sampling_app.o: ..\USER\inc\gd32f4xx_libopt.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_rcu.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_adc.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_can.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_crc.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_ctc.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_dac.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_dbg.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_dci.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_dma.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_exti.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_fmc.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_fwdgt.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_gpio.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_syscfg.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_i2c.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_iref.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_pmu.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_rtc.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_sdio.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_spi.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_timer.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_trng.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_usart.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_wwdgt.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_misc.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_enet.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_exmc.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_ipa.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Libraries\Include\gd32f4xx_tli.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\USER\inc\systick.h
.\output\sampling_app.o: ..\Components\ebtn\ebtn.h
.\output\sampling_app.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\output\sampling_app.o: ..\Components\ebtn\bit_array.h
.\output\sampling_app.o: ..\Components\oled\oled.h
.\output\sampling_app.o: ..\Components\gd25qxx\gd25qxx.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Components\sdio\sdio_sdcard.h
.\output\sampling_app.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\sampling_app.o: ..\Components\fatfs\ff.h
.\output\sampling_app.o: ..\Components\fatfs\integer.h
.\output\sampling_app.o: ..\Components\fatfs\ffconf.h
.\output\sampling_app.o: ..\Components\fatfs\diskio.h
.\output\sampling_app.o: ..\Components\fatfs\fatfs_unicode.h
.\output\sampling_app.o: ..\APP\sd_app.h
.\output\sampling_app.o: ..\APP\led_app.h
.\output\sampling_app.o: ..\APP\adc_app.h
.\output\sampling_app.o: ..\APP\oled_app.h
.\output\sampling_app.o: ..\APP\usart_app.h
.\output\sampling_app.o: ..\APP\btn_app.h
.\output\sampling_app.o: ..\APP\rtc_app.h
.\output\sampling_app.o: ..\APP\selftest_app.h
.\output\sampling_app.o: ..\Components\bsp\mcu_cmic_gd32f470vet6.h
.\output\sampling_app.o: ..\APP\sampling_app.h
.\output\sampling_app.o: ..\APP\scheduler.h
.\output\sampling_app.o: C:\Keil_v5\GorgonMeducer\perf_counter\2.4.0\perf_counter.h
.\output\sampling_app.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\output\sampling_app.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\output\sampling_app.o: C:\Keil_v5\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h
.\output\sampling_app.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdarg.h
.\output\sampling_app.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
