/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/14
* Note: 系统自检应用层实现
*/

#include "selftest_app.h"

// Flash芯片型号数据库
static const flash_chip_info_t flash_chip_db[] = {
    {0xC84017, "GD25Q64C"},
    {0xC84016, "GD25Q32C"},
    {0xC84015, "GD25Q16C"},
    {0xC84014, "GD25Q80C"},
    {0xC84013, "GD25Q40C"},
    {0xEF4017, "W25Q64FV"},
    {0xEF4016, "W25Q32FV"},
    {0xEF4015, "W25Q16FV"},
    {0x1C3117, "EN25QH64A"},
    {0x204017, "XM25QH64A"},
    {0x0, "Unknown"}
};

const char* get_flash_chip_name(uint32_t flash_id)
{
    for(int i = 0; flash_chip_db[i].id != 0; i++) {
        if(flash_chip_db[i].id == flash_id) {
            return flash_chip_db[i].name;
        }
    }
    return "Unknown";
}

selftest_result_t flash_selftest(void)
{
    uint32_t flash_id = spi_flash_read_id();
    const char* chip_name = get_flash_chip_name(flash_id);
    
    if(flash_id == 0x000000 || flash_id == 0xFFFFFF) {
        my_printf(DEBUG_USART, "flash..........fail\r\n");
        return SELFTEST_FAIL;
    }
    
    my_printf(DEBUG_USART, "flash..........ok\r\n");
    my_printf(DEBUG_USART, "flash ID:%s\r\n", chip_name);
    return SELFTEST_OK;
}

selftest_result_t tf_card_selftest(void)
{
    DSTATUS stat;
    uint16_t retry = 3;
    
    // 尝试初始化SD卡
    do {
        stat = disk_initialize(0);
        retry--;
    } while((stat != 0) && (retry > 0));
    
    if(stat != 0) {
        my_printf(DEBUG_USART, "TF card........fail\r\n");
        my_printf(DEBUG_USART, "can not find TF card\r\n");
        return SELFTEST_FAIL;
    }
    
    // 获取SD卡容量
    uint32_t capacity_kb = sd_card_capacity_get();
    float capacity_gb = capacity_kb / 1024.0f / 1024.0f;
    
    my_printf(DEBUG_USART, "TF card........ok\r\n");
    
    if(capacity_gb >= 1.0f) {
        my_printf(DEBUG_USART, "TF card memory:%.2fGB\r\n", capacity_gb);
    } else {
        my_printf(DEBUG_USART, "TF card memory:%luMB\r\n", capacity_kb / 1024);
    }
    
    return SELFTEST_OK;
}

void system_selftest(void)
{
    char time_str[32];
    
    my_printf(DEBUG_USART, "======system selftest ======\r\n");
    
    // Flash自检
    flash_selftest();
    
    // TF卡自检
    tf_card_selftest();
    
    // RTC自检
    rtc_get_time_string(time_str, sizeof(time_str));
    my_printf(DEBUG_USART, "RTC :%s\r\n", time_str);
    
    my_printf(DEBUG_USART, "======system selftest ======\r\n");
}
