--cpu=Cortex-M4.fp.sp
".\output\gd32f4xx_it.o"
".\output\main.o"
".\output\systick.o"
".\output\mcu_cmic_gd32f470vet6.o"
".\output\gd25qxx.o"
".\output\lfs.o"
".\output\lfs_port.o"
".\output\lfs_util.o"
".\output\oled.o"
".\output\ebtn.o"
".\output\sdio_sdcard.o"
".\output\ff.o"
".\output\diskio.o"
".\output\fatfs_unicode.o"
".\output\btn_app.o"
".\output\led_app.o"
".\output\oled_app.o"
".\output\scheduler.o"
".\output\usart_app.o"
".\output\sd_app.o"
".\output\adc_app.o"
".\output\rtc_app.o"
".\output\selftest_app.o"
".\output\gd32f4xx_adc.o"
".\output\gd32f4xx_can.o"
".\output\gd32f4xx_crc.o"
".\output\gd32f4xx_ctc.o"
".\output\gd32f4xx_dac.o"
".\output\gd32f4xx_dbg.o"
".\output\gd32f4xx_dci.o"
".\output\gd32f4xx_dma.o"
".\output\gd32f4xx_enet.o"
".\output\gd32f4xx_exmc.o"
".\output\gd32f4xx_exti.o"
".\output\gd32f4xx_fmc.o"
".\output\gd32f4xx_fwdgt.o"
".\output\gd32f4xx_gpio.o"
".\output\gd32f4xx_i2c.o"
".\output\gd32f4xx_ipa.o"
".\output\gd32f4xx_iref.o"
".\output\gd32f4xx_misc.o"
".\output\gd32f4xx_pmu.o"
".\output\gd32f4xx_rcu.o"
".\output\gd32f4xx_rtc.o"
".\output\gd32f4xx_sdio.o"
".\output\gd32f4xx_spi.o"
".\output\gd32f4xx_syscfg.o"
".\output\gd32f4xx_timer.o"
".\output\gd32f4xx_tli.o"
".\output\gd32f4xx_trng.o"
".\output\gd32f4xx_usart.o"
".\output\gd32f4xx_wwdgt.o"
".\output\startup_gd32f450_470.o"
".\output\system_gd32f4xx.o"
".\output\perf_counter.o"
".\output\perfc_port_default.o"
".\output\systick_wrapper_ual.o"
--strict --scatter ".\output\Project.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\list\Project.map" -o .\output\Project.axf