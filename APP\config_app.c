/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/14
* Note: 配置文件管理应用层实现
*/

#include "config_app.h"

// 全局变量定义
config_params_t config_params = {50.0f, 250}; // 默认值
config_state_t config_state = CONFIG_STATE_NORMAL;

void config_app_init(void)
{
    config_params.ratio = 50.0f; // 默认ratio值
    config_params.limit = 250;   // 默认limit值
    config_state = CONFIG_STATE_NORMAL;
}

int config_read_from_tf(void)
{
    FIL config_file;
    FRESULT result;
    char line_buffer[128];
    UINT bytes_read;
    
    // 尝试打开配置文件
    result = f_open(&config_file, "0:/TF_demo/config.ini", FA_READ);
    if(result != FR_OK) {
        return 0; // 文件不存在或打开失败
    }
    
    // 读取文件内容
    while(f_gets(line_buffer, sizeof(line_buffer), &config_file) != NULL) {
        // 移除换行符
        char* newline = strchr(line_buffer, '\n');
        if(newline) *newline = '\0';
        char* carriage = strchr(line_buffer, '\r');
        if(carriage) *carriage = '\0';
        
        // 解析Ratio参数
        if(strncmp(line_buffer, "Ratio=", 6) == 0) {
            float ratio_val = atof(line_buffer + 6);
            if(ratio_val >= 0.0f && ratio_val <= 100.0f) {
                config_params.ratio = ratio_val;
            }
        }
        // 解析Limit参数
        else if(strncmp(line_buffer, "Limit=", 6) == 0) {
            int limit_val = atoi(line_buffer + 6);
            if(limit_val >= 0 && limit_val <= 500) {
                config_params.limit = limit_val;
            }
        }
    }
    
    f_close(&config_file);
    return 1; // 成功读取
}

int config_write_to_tf(void)
{
    FIL config_file;
    FRESULT result;
    UINT bytes_written;
    char write_buffer[128];
    
    // 创建TF_demo目录（如果不存在）
    f_mkdir("0:/TF_demo");
    
    // 打开配置文件进行写入
    result = f_open(&config_file, "0:/TF_demo/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if(result != FR_OK) {
        return 0; // 打开失败
    }
    
    // 写入Ratio参数
    sprintf(write_buffer, "Ratio=%.2f\r\n", config_params.ratio);
    result = f_write(&config_file, write_buffer, strlen(write_buffer), &bytes_written);
    if(result != FR_OK) {
        f_close(&config_file);
        return 0;
    }
    
    // 写入Limit参数
    sprintf(write_buffer, "Limit=%d\r\n", config_params.limit);
    result = f_write(&config_file, write_buffer, strlen(write_buffer), &bytes_written);
    if(result != FR_OK) {
        f_close(&config_file);
        return 0;
    }
    
    f_close(&config_file);
    return 1; // 成功写入
}

void config_cmd_handler(void)
{
    if(config_read_from_tf()) {
        my_printf(DEBUG_USART, "Ratio = %.2f\r\n", config_params.ratio);
        my_printf(DEBUG_USART, "Limit = %d\r\n", config_params.limit);
        my_printf(DEBUG_USART, "config read success\r\n");
    } else {
        my_printf(DEBUG_USART, "config ini not found\r\n");
    }
}

void config_ratio_handler(void)
{
    my_printf(DEBUG_USART, "Ratio = %.2f\r\n", config_params.ratio);
    my_printf(DEBUG_USART, "Input value(0-100)\r\n");
    config_state = CONFIG_STATE_WAIT_RATIO;
}

void config_limit_handler(void)
{
    my_printf(DEBUG_USART, "Limit = %d\r\n", config_params.limit);
    my_printf(DEBUG_USART, "Input value(0~500)\r\n");
    config_state = CONFIG_STATE_WAIT_LIMIT;
}

int config_set_ratio(float new_ratio)
{
    if(new_ratio >= 0.0f && new_ratio <= 100.0f) {
        config_params.ratio = new_ratio;
        config_write_to_tf(); // 保存到文件
        return 1; // 成功
    }
    return 0; // 失败
}

int config_set_limit(int new_limit)
{
    if(new_limit >= 0 && new_limit <= 500) {
        config_params.limit = new_limit;
        config_write_to_tf(); // 保存到文件
        return 1; // 成功
    }
    return 0; // 失败
}

void config_process_input(const char* input)
{
    if(config_state == CONFIG_STATE_WAIT_RATIO) {
        float new_ratio = atof(input);
        if(config_set_ratio(new_ratio)) {
            my_printf(DEBUG_USART, "ratio modified success\r\n");
            my_printf(DEBUG_USART, "Ratio = %.2f\r\n", config_params.ratio);
        } else {
            my_printf(DEBUG_USART, "ratio invalid\r\n");
            my_printf(DEBUG_USART, "Ratio = %.2f\r\n", config_params.ratio);
        }
        config_state = CONFIG_STATE_NORMAL;
    }
    else if(config_state == CONFIG_STATE_WAIT_LIMIT) {
        int new_limit = atoi(input);
        if(config_set_limit(new_limit)) {
            my_printf(DEBUG_USART, "limit modified success\r\n");
            my_printf(DEBUG_USART, "Limit = %d\r\n", config_params.limit);
        } else {
            my_printf(DEBUG_USART, "limit invalid\r\n");
            my_printf(DEBUG_USART, "Limit = %d\r\n", config_params.limit);
        }
        config_state = CONFIG_STATE_NORMAL;
    }
}
